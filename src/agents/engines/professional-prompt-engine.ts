/**
 * Professional Prompt Engine
 * Manages and applies professional Agent prompts for Sub-Agents system
 * v1.4.0 - Sub-Agents Revolution
 */

import { getAgentPrompt, formatPrompt, AgentPrompt } from '../prompts/professional-prompts.js';

export interface PromptContext {
  agentType: string;
  taskType: string;
  variables: { [key: string]: any };
  qualityThreshold?: number;
  outputFormat?: string;
}

export interface PromptResult {
  systemPrompt: string;
  taskPrompt: string;
  qualityStandards: string[];
  expectedFormat: string;
  roleDescription: string;
  confidence: number;
}

/**
 * Professional Prompt Engine Class
 * Generates professional prompts for Sub-Agents workflow
 */
export class ProfessionalPromptEngine {
  private static instance: ProfessionalPromptEngine;
  private promptCache: Map<string, AgentPrompt> = new Map();

  private constructor() {}

  static getInstance(): ProfessionalPromptEngine {
    if (!ProfessionalPromptEngine.instance) {
      ProfessionalPromptEngine.instance = new ProfessionalPromptEngine();
    }
    return ProfessionalPromptEngine.instance;
  }

  /**
   * Generate complete professional prompt
   */
  generatePrompt(context: PromptContext): PromptResult {
    const agentPrompt = this.getAgentPrompt(context.agentType);
    if (!agentPrompt) {
      throw new Error(`Unknown agent type: ${context.agentType}`);
    }

    const taskPrompt = this.getTaskPrompt(agentPrompt, context.taskType, context.variables);
    const systemPrompt = this.buildSystemPrompt(agentPrompt, context);

    return {
      systemPrompt,
      taskPrompt,
      qualityStandards: agentPrompt.qualityStandards,
      expectedFormat: context.outputFormat || agentPrompt.outputFormat,
      roleDescription: this.buildRoleDescription(agentPrompt),
      confidence: this.calculateConfidence(context)
    };
  }

  /**
   * Get Agent prompt configuration
   */
  private getAgentPrompt(agentType: string): AgentPrompt | null {
    // Check cache first
    if (this.promptCache.has(agentType)) {
      return this.promptCache.get(agentType)!;
    }

    // Get from configuration and cache
    const prompt = getAgentPrompt(agentType);
    if (prompt) {
      this.promptCache.set(agentType, prompt);
    }

    return prompt;
  }

  /**
   * Get task-specific prompt
   */
  private getTaskPrompt(agentPrompt: AgentPrompt, taskType: string, variables: { [key: string]: any }): string {
    const taskTemplate = agentPrompt.taskPrompts[taskType];
    if (!taskTemplate) {
      throw new Error(`Unknown task type: ${taskType} for agent`);
    }

    return formatPrompt(taskTemplate, variables);
  }

  /**
   * Build system prompt
   */
  private buildSystemPrompt(agentPrompt: AgentPrompt, context: PromptContext): string {
    const qualityThreshold = context.qualityThreshold || 85;
    
    return `${agentPrompt.systemPrompt}

🎯 **Current Task Objective**
You need to complete the ${context.taskType} task as a ${agentPrompt.role}.

📊 **Quality Requirements**
- Minimum quality standard: ${qualityThreshold}%
- Must follow these quality standards:
${agentPrompt.qualityStandards.map(standard => `  • ${standard}`).join('\n')}

📝 **Output Format**
${context.outputFormat || agentPrompt.outputFormat}

⚡ **Working Style**
${agentPrompt.personality}

🔧 **Professional Skills**
${agentPrompt.expertise.map(skill => `• ${skill}`).join('\n')}

Please strictly follow the above requirements to complete the task and ensure output quality meets professional standards.`;
  }

  /**
   * Build role description
   */
  private buildRoleDescription(agentPrompt: AgentPrompt): string {
    return `You are a ${agentPrompt.role}, with expertise in: ${agentPrompt.expertise.join(', ')}. Your working style is: ${agentPrompt.personality}.`;
  }

  /**
   * Calculate confidence based on context completeness
   */
  private calculateConfidence(context: PromptContext): number {
    let confidence = 70; // Base confidence
    
    if (context.variables && typeof context.variables === 'object') {
      const keys = Object.keys(context.variables);
      confidence += Math.min(keys.length * 5, 20); // Data completeness bonus
    }
    
    if (context.qualityThreshold && context.qualityThreshold >= 90) {
      confidence += 5; // High quality threshold bonus
    }
    
    return Math.min(confidence, 95);
  }

  /**
   * Validate prompt quality
   */
  validatePrompt(prompt: PromptResult): boolean {
    // Check required fields
    if (!prompt.systemPrompt || !prompt.taskPrompt) {
      return false;
    }

    // Check prompt length
    if (prompt.systemPrompt.length < 100 || prompt.taskPrompt.length < 50) {
      return false;
    }

    // Check quality standards
    if (!prompt.qualityStandards || prompt.qualityStandards.length === 0) {
      return false;
    }

    return true;
  }

  /**
   * Get all available Agent types
   */
  getAvailableAgentTypes(): string[] {
    return ['spec', 'architect', 'developer', 'quality', 'test'];
  }

  /**
   * Get available task types for specified Agent
   */
  getAvailableTaskTypes(agentType: string): string[] {
    const agentPrompt = this.getAgentPrompt(agentType);
    if (!agentPrompt) {
      return [];
    }

    return Object.keys(agentPrompt.taskPrompts);
  }

  /**
   * Clear prompt cache
   */
  clearCache(): void {
    this.promptCache.clear();
  }
}

/**
 * Professional Prompt Factory
 * Convenient prompt generation interface for Sub-Agents workflow
 */
export class ProfessionalPromptFactory {
  private static engine = ProfessionalPromptEngine.getInstance();

  /**
   * Generate prompt for requirements analysis
   */
  static forRequirementsAnalysis(description: string, title: string, qualityThreshold = 90): PromptResult {
    return this.engine.generatePrompt({
      agentType: 'spec',
      taskType: 'analyze-requirements',
      variables: { description, title },
      qualityThreshold
    });
  }

  /**
   * Generate prompt for architecture design
   */
  static forArchitectureDesign(requirements: any, scale: string, constraints: string, qualityThreshold = 85): PromptResult {
    return this.engine.generatePrompt({
      agentType: 'architect',
      taskType: 'design-architecture',
      variables: { requirements, scale, constraints },
      qualityThreshold
    });
  }

  /**
   * Generate prompt for feature implementation
   */
  static forFeatureImplementation(architecture: any, requirements: any, constraints: string, qualityThreshold = 85): PromptResult {
    return this.engine.generatePrompt({
      agentType: 'developer',
      taskType: 'implement-features',
      variables: { architecture, requirements, constraints },
      qualityThreshold
    });
  }

  /**
   * Generate prompt for quality analysis
   */
  static forQualityAnalysis(code: string, architecture: any, testCoverage: any, qualityThreshold = 75): PromptResult {
    return this.engine.generatePrompt({
      agentType: 'quality',
      taskType: 'analyze-quality',
      variables: { code, architecture, testCoverage },
      qualityThreshold
    });
  }

  /**
   * Generate prompt for test strategy
   */
  static forTestStrategy(requirements: any, architecture: any, qualityGoals: any, qualityThreshold = 70): PromptResult {
    return this.engine.generatePrompt({
      agentType: 'test',
      taskType: 'design-test-strategy',
      variables: { requirements, architecture, qualityGoals },
      qualityThreshold
    });
  }
}

// Export singleton instance
export const professionalPromptEngine = ProfessionalPromptEngine.getInstance();
