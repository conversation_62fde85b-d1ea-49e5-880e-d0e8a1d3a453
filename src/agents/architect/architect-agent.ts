/**
 * Architect Agent - 架构代理
 * v1.4.0 - Sub-Agents 革命
 */

import { BaseAgent } from '../base-agent.js';
import {
  AgentMessage,
  AgentContext,
  ArchitectureState
} from '../types.js';
import { AIArchitectureAnalysisEngine } from '../engines/ai-analysis-engine.js';

export class ArchitectAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super('architect', context);
  }

  protected async onStart(): Promise<void> {
    console.log('🏗️ Architect Agent ready to design architecture');
  }

  protected async onStop(): Promise<void> {
    console.log('🏗️ Architect Agent stopped');
  }

  protected async processMessage(message: AgentMessage): Promise<AgentMessage | null> {
    switch (message.type) {
      case 'request':
        return await this.handleRequest(message);
      default:
        return null;
    }
  }

  private async handleRequest(message: AgentMessage): Promise<AgentMessage | null> {
    const { action, data } = message.payload;

    switch (action) {
      case 'design-architecture':
        return await this.designArchitecture(data, message);
      default:
        return {
          id: `error-${Date.now()}`,
          type: 'error',
          to: message.from,
          payload: { error: `Unknown action: ${action}` },
          timestamp: new Date().toISOString(),
          correlationId: message.correlationId
        };
    }
  }

  private async designArchitecture(data: any, originalMessage: AgentMessage): Promise<AgentMessage> {
    this.setCurrentTask('Designing system architecture');

    try {
      console.log(`🏗️ Designing architecture for: ${data.requirements?.title}`);

      // 使用 AI 驱动的智能架构分析引擎
      const analysisEngine = new AIArchitectureAnalysisEngine({
        projectRoot: this.context.projectRoot,
        description: data.requirements?.description || 'System architecture',
        title: data.requirements?.title || 'Architecture Design',
        requirements: data.requirements
      });

      const analysisResult = await analysisEngine.analyze();
      const qualityScore = analysisResult.quality;

      const architectureState: ArchitectureState = {
        designDocument: analysisResult.data.designDocument,
        technicalStack: analysisResult.data.technicalStack,
        apiDesign: analysisResult.data.apiDesign,
        dataModel: analysisResult.data.dataModel,
        qualityScore
      };

      // 更新共享状态
      this.updateSharedState({ architecture: architectureState });

      // 检查质量门控
      const qualityPassed = await this.checkQualityGates('architecture', qualityScore);

      console.log(`🏗️ Architecture designed with quality score: ${qualityScore}% (confidence: ${analysisResult.confidence}%)`);

      return {
        id: `arch-response-${Date.now()}`,
        type: 'response',
        from: this.agentId,
        to: originalMessage.from,
        payload: {
          phase: 'architecture',
          success: qualityPassed,
          result: architectureState,
          qualityScore,
          confidence: analysisResult.confidence,
          metadata: analysisResult.metadata,
          message: qualityPassed ? 'Architecture designed successfully' : 'Architecture quality below threshold'
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };

    } catch (error) {
      console.error('❌ Error designing architecture:', error);

      return {
        id: `arch-error-${Date.now()}`,
        type: 'response',
        from: this.agentId,
        to: originalMessage.from,
        payload: {
          phase: 'architecture',
          success: false,
          error: error instanceof Error ? error.message : String(error),
          qualityScore: 0
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };
    }
  }

  // 所有硬编码的架构设计方法已被 ArchitectureAnalysisEngine 替代
  // 这提供了更智能、可配置的架构设计能力

  // 所有硬编码的架构设计方法已被 ArchitectureAnalysisEngine 替代

  async executeTask(taskData: any): Promise<any> {
    return await this.designArchitecture(taskData, {
      id: 'direct-task',
      type: 'request',
      from: 'user',
      to: this.agentId,
      payload: { action: 'design-architecture', data: taskData },
      timestamp: new Date().toISOString()
    });
  }

  getCapabilities(): string[] {
    return [
      'System architecture design',
      'Technical stack selection',
      'API design and specification',
      'Data model design',
      'Architecture quality assessment',
      'Scalability and performance planning'
    ];
  }
}
