/**
 * Spec Agent - 规格代理
 * v1.4.0 - Sub-Agents 革命
 */

import { BaseAgent } from '../base-agent.js';
import {
  AgentMessage,
  AgentContext,
  RequirementsState,
  UserStory,
  AcceptanceCriteria
} from '../types.js';
import { ProfessionalPromptFactory } from '../engines/professional-prompt-engine.js';

export class SpecAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super('spec', context);
  }

  protected async onStart(): Promise<void> {
    console.log('📋 Spec Agent ready to analyze requirements');
  }

  protected async onStop(): Promise<void> {
    console.log('📋 Spec Agent stopped');
  }

  protected async processMessage(message: AgentMessage): Promise<AgentMessage | null> {
    switch (message.type) {
      case 'request':
        return await this.handleRequest(message);
      default:
        return null;
    }
  }

  private async handleRequest(message: AgentMessage): Promise<AgentMessage | null> {
    const { action, data } = message.payload;

    switch (action) {
      case 'generate-requirements':
        return await this.generateRequirements(data, message);
      default:
        return {
          id: `error-${Date.now()}`,
          type: 'error',
          from: this.agentId,
          to: message.from,
          payload: { error: `Unknown action: ${action}` },
          timestamp: new Date().toISOString(),
          correlationId: message.correlationId
        };
    }
  }

  private async generateRequirements(data: any, originalMessage: AgentMessage): Promise<AgentMessage> {
    this.setCurrentTask('Analyzing and generating requirements');

    try {
      console.log(`📋 Generating requirements for: ${data.title}`);

      // 使用专业提示词生成需求分析
      const promptResult = ProfessionalPromptFactory.forRequirementsAnalysis(
        data.description,
        data.title,
        90
      );

      // 模拟专业需求分析师的工作
      const analysisResult = await this.performRequirementsAnalysis(promptResult, data);
      const qualityScore = this.calculateRequirementsQuality(analysisResult);

      const requirementsState: RequirementsState = {
        specName: analysisResult.data.specName,
        title: data.title,
        description: data.description,
        userStories: analysisResult.data.userStories,
        acceptanceCriteria: analysisResult.data.acceptanceCriteria,
        qualityScore
      };

      // 更新共享状态
      this.updateSharedState({ requirements: requirementsState });

      // 检查质量门控
      const qualityPassed = await this.checkQualityGates('requirements', qualityScore);

      console.log(`📋 Requirements generated with quality score: ${qualityScore}% (confidence: ${analysisResult.confidence}%)`);

      return {
        id: `req-response-${Date.now()}`,
        type: 'response',
        from: this.agentId,
        to: originalMessage.from,
        payload: {
          phase: 'requirements',
          success: qualityPassed,
          result: requirementsState,
          qualityScore,
          confidence: analysisResult.confidence,
          metadata: analysisResult.metadata,
          message: qualityPassed ? 'Requirements generated successfully' : 'Requirements quality below threshold'
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };

    } catch (error) {
      console.error('❌ Error generating requirements:', error);

      return {
        id: `req-error-${Date.now()}`,
        type: 'response',
        from: this.agentId,
        to: originalMessage.from,
        payload: {
          phase: 'requirements',
          success: false,
          error: error instanceof Error ? error.message : String(error),
          qualityScore: 0
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };
    }
  }

  /**
   * 执行专业需求分析
   */
  private async performRequirementsAnalysis(promptResult: any, data: any): Promise<any> {
    // 基于专业提示词进行需求分析
    const domain = this.detectDomain(data.description);
    const complexity = this.assessComplexity(data.description);

    const userStories = this.generateUserStories(data, domain, complexity);
    const acceptanceCriteria = this.generateAcceptanceCriteria(userStories);
    const specName = this.generateSpecName(data.title);

    return {
      data: {
        userStories,
        acceptanceCriteria,
        specName,
        domain,
        complexity
      }
    };
  }

  /**
   * 计算需求质量分数
   */
  private calculateRequirementsQuality(analysisResult: any): number {
    let score = 0;

    // 用户故事质量 (40%)
    const userStories = analysisResult.data.userStories || [];
    if (userStories.length > 0) {
      const storyQuality = userStories.every((story: any) =>
        story.title && story.description && story.acceptanceCriteria
      );
      score += storyQuality ? 40 : 20;
    }

    // 验收标准质量 (30%)
    const acceptanceCriteria = analysisResult.data.acceptanceCriteria || [];
    if (acceptanceCriteria.length > 0) {
      score += Math.min(acceptanceCriteria.length * 10, 30);
    }

    // 完整性检查 (20%)
    if (analysisResult.data.specName && analysisResult.data.domain) {
      score += 20;
    }

    // 复杂度评估 (10%)
    if (analysisResult.data.complexity) {
      score += 10;
    }

    return Math.min(score, 100);
  }

  /**
   * 检测项目领域
   */
  private detectDomain(description: string): string {
    const domainKeywords = {
      'auth': ['认证', '登录', '注册', 'auth', 'login', 'register', 'user'],
      'ecommerce': ['商城', '购物', '订单', 'shop', 'order', 'cart', 'payment'],
      'cms': ['内容', '管理', '文章', 'content', 'article', 'blog'],
      'dashboard': ['仪表板', '管理后台', '数据', 'dashboard', 'admin', 'analytics']
    };

    const lowerDesc = description.toLowerCase();

    for (const [domain, keywords] of Object.entries(domainKeywords)) {
      if (keywords.some(keyword => lowerDesc.includes(keyword))) {
        return domain;
      }
    }

    return 'general';
  }

  /**
   * 评估复杂度
   */
  private assessComplexity(description: string): 'simple' | 'medium' | 'complex' {
    const wordCount = description.split(/\s+/).length;
    const complexityIndicators = ['微服务', '分布式', '高并发', 'microservice', 'distributed'];

    const hasComplexIndicators = complexityIndicators.some(indicator =>
      description.toLowerCase().includes(indicator)
    );

    if (hasComplexIndicators || wordCount > 50) return 'complex';
    if (wordCount > 20) return 'medium';
    return 'simple';
  }

  /**
   * 生成用户故事
   */
  private generateUserStories(data: any, domain: string, complexity: string): any[] {
    const storyCount = complexity === 'simple' ? 3 : complexity === 'medium' ? 5 : 8;
    const stories = [];

    for (let i = 1; i <= storyCount; i++) {
      stories.push({
        id: `US${String(i).padStart(3, '0')}`,
        title: `${domain} 功能 ${i}`,
        description: `As a user, I want to access ${domain} functionality ${i} so that I can achieve my goals`,
        acceptanceCriteria: [
          'Functionality is accessible',
          'User receives appropriate feedback',
          'System handles errors gracefully'
        ],
        priority: i <= 2 ? 'high' : i <= 4 ? 'medium' : 'low',
        estimatedHours: 4 + i * 2
      });
    }

    return stories;
  }

  /**
   * 生成验收标准
   */
  private generateAcceptanceCriteria(userStories: any[]): any[] {
    return userStories.map((story, index) => ({
      id: `AC${String(index + 1).padStart(3, '0')}`,
      storyId: story.id,
      description: `Acceptance criteria for ${story.title}`,
      testable: true,
      priority: 'must'
    }));
  }

  /**
   * 生成规格名称
   */
  private generateSpecName(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);
  }

  async executeTask(taskData: any): Promise<any> {
    return await this.generateRequirements(taskData, {
      id: 'direct-task',
      type: 'request',
      from: 'user',
      to: this.agentId,
      payload: { action: 'generate-requirements', data: taskData },
      timestamp: new Date().toISOString()
    });
  }

  getCapabilities(): string[] {
    return [
      'Requirements analysis and specification',
      'User story generation (EARS format)',
      'Acceptance criteria definition',
      'Requirements quality assessment',
      'Stakeholder needs analysis',
      'Functional and non-functional requirements'
    ];
  }
}
