/**
 * Simplified Specification workflow MCP tools
 * No configuration files, all parameters via tool calls
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { z } from "zod";
import { ROOT_PATH_DESC } from "../utils/const.js";

/**
 * Register simplified specification workflow tools
 */
export function registerSpecTools(server: McpServer): void {
  // Create specification
  server.tool(
    "spec-create",
    "Create new specification workflow",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
      title: z.string().describe("Specification title"),
      description: z.string().describe("Specification description"),
    },
    async ({ rootPath, title, description }) => {
      try {
        // Ensure .vibecode directory exists
        const vibecodePath = path.join(rootPath, ".vibecode");
        if (!existsSync(vibecodePath)) {
          await fs.mkdir(vibecodePath, { recursive: true });
        }

        // Create specs directory
        const specsPath = path.join(vibecodePath, "specs");
        if (!existsSync(specsPath)) {
          await fs.mkdir(specsPath, { recursive: true });
        }

        // Generate spec name from title
        const specName = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
        const specPath = path.join(specsPath, `${specName}.md`);

        // Check if spec already exists
        if (existsSync(specPath)) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Specification "${title}" already exists at ${specPath}`,
              },
            ],
          };
        }

        // Create specification document
        const timestamp = new Date().toISOString();
        const specContent = `# ${title}

**Created:** ${timestamp}
**Status:** Created

## Description
${description}

## Requirements
_To be defined_

## Design
_To be defined_

## Tasks
_To be defined_

## Implementation Notes
_To be added during implementation_
`;

        await fs.writeFile(specPath, specContent, "utf-8");

        return {
          content: [
            {
              type: "text",
              text: `✅ Specification "${title}" created successfully at ${specPath}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Error creating specification: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // List specifications
  server.tool(
    "spec-list",
    "List all specifications",
    {
      type: "object",
      properties: {
        rootPath: {
          type: "string",
          description: ROOT_PATH_DESC,
        },
      },
      required: ["rootPath"],
      additionalProperties: false,
    },
    async ({ rootPath }) => {
      try {
        const specsPath = path.join(rootPath, ".vibecode", "specs");

        if (!existsSync(specsPath)) {
          return {
            content: [
              {
                type: "text",
                text: "📋 No specifications found. Use spec-create to create your first specification.",
              },
            ],
          };
        }

        const files = await fs.readdir(specsPath);
        const specFiles = files.filter(file => file.endsWith('.md'));

        if (specFiles.length === 0) {
          return {
            content: [
              {
                type: "text",
                text: "📋 No specifications found. Use spec-create to create your first specification.",
              },
            ],
          };
        }

        const specs = [];
        for (const file of specFiles) {
          const filePath = path.join(specsPath, file);
          const content = await fs.readFile(filePath, "utf-8");
          const lines = content.split('\n');
          const title = lines[0]?.replace(/^#\s*/, '') || file.replace('.md', '');
          const statusLine = lines.find(line => line.startsWith('**Status:**'));
          const status = statusLine?.replace('**Status:**', '').trim() || 'Unknown';

          specs.push(`• ${title} (${file}) - Status: ${status}`);
        }

        return {
          content: [
            {
              type: "text",
              text: `📋 Found ${specs.length} specification(s):\n\n${specs.join('\n')}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Error listing specifications: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // View specification status
  server.tool(
    "spec-status",
    "View current status of specifications",
    {
      type: "object",
      properties: {
        rootPath: {
          type: "string",
          description: ROOT_PATH_DESC,
        },
        specName: {
          type: "string",
          description: "Optional: specific specification name to check",
        },
      },
      required: ["rootPath"],
      additionalProperties: false,
    },
    async ({ rootPath, specName }) => {
      try {
        const specsPath = path.join(rootPath, ".vibecode", "specs");

        if (!existsSync(specsPath)) {
          return {
            content: [
              {
                type: "text",
                text: "📋 No specifications directory found.",
              },
            ],
          };
        }

        if (specName) {
          // Show specific spec
          const specPath = path.join(specsPath, `${specName}.md`);
          if (!existsSync(specPath)) {
            return {
              content: [
                {
                  type: "text",
                  text: `❌ Specification "${specName}" not found.`,
                },
              ],
            };
          }

          const content = await fs.readFile(specPath, "utf-8");
          return {
            content: [
              {
                type: "text",
                text: `📄 Specification: ${specName}\n\n${content}`,
              },
            ],
          };
        } else {
          // Show all specs status
          const files = await fs.readdir(specsPath);
          const specFiles = files.filter(file => file.endsWith('.md'));

          if (specFiles.length === 0) {
            return {
              content: [
                {
                  type: "text",
                  text: "📋 No specifications found.",
                },
              ],
            };
          }

          const statusList = [];
          for (const file of specFiles) {
            const filePath = path.join(specsPath, file);
            const content = await fs.readFile(filePath, "utf-8");
            const lines = content.split('\n');
            const title = lines[0]?.replace(/^#\s*/, '') || file.replace('.md', '');
            const statusLine = lines.find(line => line.startsWith('**Status:**'));
            const status = statusLine?.replace('**Status:**', '').trim() || 'Unknown';

            statusList.push(`📄 ${title}: ${status}`);
          }

          return {
            content: [
              {
                type: "text",
                text: `📊 Specifications Status:\n\n${statusList.join('\n')}`,
              },
            ],
          };
        }
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Error checking specification status: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );
}
