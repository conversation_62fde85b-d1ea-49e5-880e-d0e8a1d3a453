/**
 * Simplified Specification workflow MCP tools
 * No configuration files, all parameters via tool calls
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { z } from "zod";
import { ROOT_PATH_DESC } from "../utils/const.js";
import { ensureWorkflowDirectories, generateSpecName, getCurrentTimestamp, getSpecsDir } from "../utils/utils.js";
import { SpecConfig } from "../types.js";

/**
 * Register simplified specification workflow tools
 */
export function registerSpecTools(server: McpServer): void {
  // Create specification
  server.tool(
    "spec-create",
    "Create new specification workflow",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
      title: z.string().describe("Meaningful specification title"),
      description: z.string().optional().describe("Meaningful specification description"),
    },
    async ({ rootPath, title, description }) => {
      try {
        // Ensure directory exists
        await ensureWorkflowDirectories(rootPath);

        // Generate spec name from title
        const specName = generateSpecName(title);
        const specDir = path.join(getSpecsDir(rootPath), specName);

        // Check if spec already exists
        if (existsSync(specDir)) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Specification "${title}" already exists at ${specDir}. Please use a different title or delete the existing specification.`,
              },
            ],
          };
        }

        // Create specification configuration
        const specConfig: SpecConfig = {
          name: specName,
          title,
          status: 'created',
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp(),
        };

        // Create specification document
        await fs.mkdir(specDir, { recursive: true });
        // Create initial document
        const overviewContent = `# ${title}

## Overview
${description || "Please add specification description"}

## Status
- Current status: ${specConfig.status}
- Created at: ${specConfig.createdAt}
- Last updated: ${specConfig.updatedAt}

## Workflow
1. ✅ Create specification (completed)
2. ⏳ Requirements analysis (pending) - Use \`spec-requirements\`
3. ⏳ Design document (pending) - Use \`spec-design\`
4. ⏳ Task breakdown (pending) - Use \`spec-tasks\`
5. ⏳ Implementation (pending) - Use \`spec-execute\`

## Next Step
Run \`spec-requirements\` to start the requirements analysis phase.
`;

        await fs.writeFile(path.join(specDir, "overview.md"), overviewContent, "utf-8");

        return {
          content: [
            {
              type: "text",
              text: `✅ Specification "${title}" created successfully!\n\nSpecification name: ${specName}\nStatus: ${specConfig.status}\n\nNext step: Run \`spec-requirements\` to start the requirements analysis phase.`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Error creating specification: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // List specifications
  server.tool(
    "spec-list",
    "List all specifications",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
    },
    async ({ rootPath }) => {
      try {
        const specsPath = path.join(rootPath, ".vibecode", "specs");

        if (!existsSync(specsPath)) {
          return {
            content: [
              {
                type: "text",
                text: "📋 No specifications found. Use spec-create to create your first specification.",
              },
            ],
          };
        }

        const files = await fs.readdir(specsPath);
        const specFiles = files.filter(file => file.endsWith('.md'));

        if (specFiles.length === 0) {
          return {
            content: [
              {
                type: "text",
                text: "📋 No specifications found. Use spec-create to create your first specification.",
              },
            ],
          };
        }

        const specs = [];
        for (const file of specFiles) {
          const filePath = path.join(specsPath, file);
          const content = await fs.readFile(filePath, "utf-8");
          const lines = content.split('\n');
          const title = lines[0]?.replace(/^#\s*/, '') || file.replace('.md', '');
          const statusLine = lines.find(line => line.startsWith('**Status:**'));
          const status = statusLine?.replace('**Status:**', '').trim() || 'Unknown';

          specs.push(`• ${title} (${file}) - Status: ${status}`);
        }

        return {
          content: [
            {
              type: "text",
              text: `📋 Found ${specs.length} specification(s):\n\n${specs.join('\n')}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Error listing specifications: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // View specification status
  server.tool(
    "spec-status",
    "View current status of specifications",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
      specName: z.string().optional().describe("Optional: specific specification name to check"),
    },
    async ({ rootPath, specName }) => {
      try {
        const specsPath = path.join(rootPath, ".vibecode", "specs");

        if (!existsSync(specsPath)) {
          return {
            content: [
              {
                type: "text",
                text: "📋 No specifications directory found.",
              },
            ],
          };
        }

        if (specName) {
          // Show specific spec
          const specPath = path.join(specsPath, `${specName}.md`);
          if (!existsSync(specPath)) {
            return {
              content: [
                {
                  type: "text",
                  text: `❌ Specification "${specName}" not found.`,
                },
              ],
            };
          }

          const content = await fs.readFile(specPath, "utf-8");
          return {
            content: [
              {
                type: "text",
                text: `📄 Specification: ${specName}\n\n${content}`,
              },
            ],
          };
        } else {
          // Show all specs status
          const files = await fs.readdir(specsPath);
          const specFiles = files.filter(file => file.endsWith('.md'));

          if (specFiles.length === 0) {
            return {
              content: [
                {
                  type: "text",
                  text: "📋 No specifications found.",
                },
              ],
            };
          }

          const statusList = [];
          for (const file of specFiles) {
            const filePath = path.join(specsPath, file);
            const content = await fs.readFile(filePath, "utf-8");
            const lines = content.split('\n');
            const title = lines[0]?.replace(/^#\s*/, '') || file.replace('.md', '');
            const statusLine = lines.find(line => line.startsWith('**Status:**'));
            const status = statusLine?.replace('**Status:**', '').trim() || 'Unknown';

            statusList.push(`📄 ${title}: ${status}`);
          }

          return {
            content: [
              {
                type: "text",
                text: `📊 Specifications Status:\n\n${statusList.join('\n')}`,
              },
            ],
          };
        }
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Error checking specification status: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );
}
