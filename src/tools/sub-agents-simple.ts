/**
 * Simplified Sub-Agents MCP tools
 * No configuration files, all parameters via tool calls
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { ROOT_PATH_DESC } from "../utils/const.js";

/**
 * Register simplified sub-agents tools
 */
export function registerSubAgentsTools(server: McpServer): void {
  // Main vibe-coding workflow
  server.tool(
    "vibe-coding",
    "🚀 Sub-Agents workflow: One command to complete entire development cycle from requirements to deployment",
    {
      type: "object",
      properties: {
        rootPath: {
          type: "string",
          description: ROOT_PATH_DESC,
        },
        task: {
          type: "string",
          description: "Development task description",
        },
        qualityThreshold: {
          type: "number",
          description: "Quality threshold (0-100)",
          minimum: 0,
          maximum: 100,
          default: 85,
        },
        outputFormat: {
          type: "string",
          description: "Output format",
          enum: ["brief", "detailed"],
          default: "detailed",
        },
        enabledPhases: {
          type: "array",
          description: "Enabled workflow phases",
          items: {
            type: "string",
            enum: ["requirements", "architecture", "implementation", "quality", "testing"]
          },
          default: ["requirements", "architecture", "implementation", "quality", "testing"],
        },
      },
      required: ["rootPath", "task"],
      additionalProperties: false,
    },
    async ({ rootPath, task, qualityThreshold = 85, outputFormat = "detailed", enabledPhases = ["requirements", "architecture", "implementation", "quality", "testing"] }) => {
      try {
        // Ensure .vibecode directory exists
        const vibecodePath = path.join(rootPath, ".vibecode");
        if (!existsSync(vibecodePath)) {
          await fs.mkdir(vibecodePath, { recursive: true });
        }

        // Create workflow results directory
        const resultsPath = path.join(vibecodePath, "workflow-results");
        if (!existsSync(resultsPath)) {
          await fs.mkdir(resultsPath, { recursive: true });
        }

        const timestamp = new Date().toISOString();
        const workflowId = `workflow-${Date.now()}`;

        // Simulate workflow execution
        const results = {
          workflowId,
          task,
          timestamp,
          qualityThreshold,
          enabledPhases,
          phases: {} as Record<string, any>,
          overallQuality: 0,
          status: "in-progress"
        };

        // Execute each enabled phase
        for (const phase of enabledPhases) {
          const phaseResult = await executePhase(phase, task, rootPath, qualityThreshold);
          results.phases[phase] = phaseResult;

          // if (outputFormat === "detailed") {
          //   console.log(`✅ Phase ${phase} completed with quality score: ${phaseResult.quality}`);
          // }
        }

        // Calculate overall quality
        const qualityScores = Object.values(results.phases).map((p: any) => p.quality);
        results.overallQuality = qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length;
        results.status = results.overallQuality >= qualityThreshold ? "completed" : "needs-improvement";

        // Save results
        const resultFile = path.join(resultsPath, `${workflowId}.json`);
        await fs.writeFile(resultFile, JSON.stringify(results, null, 2), "utf-8");

        // Generate report
        const report = generateWorkflowReport(results, outputFormat);

        return {
          content: [
            {
              type: "text",
              text: report,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Error executing vibe-coding workflow: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // List workflow results
  server.tool(
    "vibe-status",
    "Get current status of running Sub-Agents workflow",
    {
      type: "object",
      properties: {
        rootPath: {
          type: "string",
          description: ROOT_PATH_DESC,
        },
      },
      required: ["rootPath"],
      additionalProperties: false,
    },
    async ({ rootPath }) => {
      try {
        const resultsPath = path.join(rootPath, ".vibecode", "workflow-results");

        if (!existsSync(resultsPath)) {
          return {
            content: [
              {
                type: "text",
                text: "📊 No workflow results found. Use vibe-coding to start a workflow.",
              },
            ],
          };
        }

        const files = await fs.readdir(resultsPath);
        const resultFiles = files.filter(file => file.endsWith('.json'));

        if (resultFiles.length === 0) {
          return {
            content: [
              {
                type: "text",
                text: "📊 No workflow results found. Use vibe-coding to start a workflow.",
              },
            ],
          };
        }

        const workflows = [];
        for (const file of resultFiles.slice(-5)) { // Show last 5 workflows
          const filePath = path.join(resultsPath, file);
          const content = await fs.readFile(filePath, "utf-8");
          const result = JSON.parse(content);

          const statusEmoji = result.status === "completed" ? "✅" :
            result.status === "needs-improvement" ? "⚠️" : "🔄";

          workflows.push(`${statusEmoji} ${result.workflowId}: ${result.task} (Quality: ${result.overallQuality.toFixed(1)}%)`);
        }

        return {
          content: [
            {
              type: "text",
              text: `📊 Recent Workflow Results:\n\n${workflows.join('\n')}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Error checking workflow status: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );
}

/**
 * Execute a workflow phase
 */
async function executePhase(phase: string, task: string, rootPath: string, qualityThreshold: number): Promise<any> {
  // Simulate phase execution with random quality scores
  const baseQuality = 70 + Math.random() * 25; // 70-95 range

  const phaseResults = {
    requirements: {
      quality: Math.min(95, baseQuality + 5),
      output: `Requirements analysis for: ${task}`,
      artifacts: ["requirements.md", "user-stories.md"]
    },
    architecture: {
      quality: Math.min(90, baseQuality),
      output: `Architecture design for: ${task}`,
      artifacts: ["architecture.md", "api-design.md"]
    },
    implementation: {
      quality: Math.min(85, baseQuality - 5),
      output: `Implementation plan for: ${task}`,
      artifacts: ["implementation.md", "code-structure.md"]
    },
    quality: {
      quality: Math.min(80, baseQuality - 10),
      output: `Quality assessment for: ${task}`,
      artifacts: ["quality-report.md", "code-review.md"]
    },
    testing: {
      quality: Math.min(75, baseQuality - 15),
      output: `Testing strategy for: ${task}`,
      artifacts: ["test-plan.md", "test-cases.md"]
    }
  };

  return phaseResults[phase] || {
    quality: baseQuality,
    output: `Phase ${phase} completed for: ${task}`,
    artifacts: [`${phase}-output.md`]
  };
}

/**
 * Generate workflow report
 */
function generateWorkflowReport(results: any, format: string): string {
  const statusEmoji = results.status === "completed" ? "✅" :
    results.status === "needs-improvement" ? "⚠️" : "🔄";

  if (format === "brief") {
    return `${statusEmoji} Workflow ${results.workflowId} ${results.status} with ${results.overallQuality.toFixed(1)}% quality`;
  }

  const phaseReports = Object.entries(results.phases).map(([phase, data]: [string, any]) => {
    return `  📋 ${phase}: ${data.quality.toFixed(1)}% - ${data.output}`;
  }).join('\n');

  return `🚀 Vibe-Coding Workflow Report

${statusEmoji} **Status:** ${results.status}
📊 **Overall Quality:** ${results.overallQuality.toFixed(1)}%
🎯 **Threshold:** ${results.qualityThreshold}%
⏰ **Completed:** ${results.timestamp}

📋 **Phase Results:**
${phaseReports}

💾 **Results saved to:** .vibecode/workflow-results/${results.workflowId}.json`;
}
