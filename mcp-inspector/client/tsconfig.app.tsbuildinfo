{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/__tests__/app.config.test.tsx", "./src/__tests__/app.routing.test.tsx", "./src/components/authdebugger.tsx", "./src/components/consoletab.tsx", "./src/components/dynamicjsonform.tsx", "./src/components/elicitationrequest.tsx", "./src/components/elicitationtab.tsx", "./src/components/historyandnotifications.tsx", "./src/components/jsoneditor.tsx", "./src/components/jsonview.tsx", "./src/components/listpane.tsx", "./src/components/oauthcallback.tsx", "./src/components/oauthdebugcallback.tsx", "./src/components/oauthflowprogress.tsx", "./src/components/pingtab.tsx", "./src/components/promptstab.tsx", "./src/components/resourcelinkview.tsx", "./src/components/resourcestab.tsx", "./src/components/rootstab.tsx", "./src/components/samplingrequest.tsx", "./src/components/samplingtab.tsx", "./src/components/sidebar.tsx", "./src/components/toolresults.tsx", "./src/components/toolstab.tsx", "./src/components/__tests__/authdebugger.test.tsx", "./src/components/__tests__/dynamicjsonform.array.test.tsx", "./src/components/__tests__/dynamicjsonform.test.tsx", "./src/components/__tests__/elicitationrequest.test.tsx", "./src/components/__tests__/elicitationtab.test.tsx", "./src/components/__tests__/historyandnotifications.test.tsx", "./src/components/__tests__/resourcestab.test.tsx", "./src/components/__tests__/sidebar.test.tsx", "./src/components/__tests__/toolstab.test.tsx", "./src/components/__tests__/samplingrequest.test.tsx", "./src/components/__tests__/samplingtab.test.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/button.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/combobox.tsx", "./src/components/ui/command.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/select.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/tooltip.tsx", "./src/lib/auth-types.ts", "./src/lib/auth.ts", "./src/lib/configurationtypes.ts", "./src/lib/constants.ts", "./src/lib/notificationtypes.ts", "./src/lib/oauth-state-machine.ts", "./src/lib/utils.ts", "./src/lib/hooks/usecompletionstate.ts", "./src/lib/hooks/useconnection.ts", "./src/lib/hooks/usedraggablepane.ts", "./src/lib/hooks/usetheme.ts", "./src/lib/hooks/usetoast.ts", "./src/lib/hooks/__tests__/useconnection.test.tsx", "./src/utils/configutils.ts", "./src/utils/escapeunicode.ts", "./src/utils/jsonutils.ts", "./src/utils/oauthutils.ts", "./src/utils/schemautils.ts", "./src/utils/__tests__/configutils.test.ts", "./src/utils/__tests__/escapeunicode.test.ts", "./src/utils/__tests__/jsonutils.test.ts", "./src/utils/__tests__/oauthutils.ts", "./src/utils/__tests__/schemautils.test.ts"], "version": "5.8.3"}