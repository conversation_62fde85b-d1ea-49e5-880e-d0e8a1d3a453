{"version": "1.4.0", "lastUpdated": "2025-07-30T15:30:00Z", "subAgents": {"qualityThreshold": 95, "outputFormat": "detailed", "autoRestart": true, "maxRetries": 3, "maxWorkflowRetries": 2, "enabledAgents": ["spec", "architect", "developer", "quality", "test"], "phaseThresholds": {"implementation": 85, "quality": 75, "testing": 70}, "saveResults": true, "showProgress": true}, "workflows": {"specs": {"enabled": true, "defaultStatus": "created"}, "bugs": {"enabled": true, "defaultStatus": "reported"}}, "analysis": {"enabled": true, "outputFormats": ["markdown", "json"]}, "generation": {"enabled": true, "supportedLanguages": ["typescript", "javascript", "python"], "qualityChecks": ["readability", "maintainability", "security"]}, "performance": {"maxResponseTime": 30000, "concurrentTasks": 5, "cacheEnabled": true}}