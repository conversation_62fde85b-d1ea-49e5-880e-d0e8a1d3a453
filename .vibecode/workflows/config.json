{"version": "1.3.0", "lastUpdated": "2025-07-30T10:30:00Z", "workflows": {"specs": {"enabled": true, "defaultStatus": "created", "statusFlow": ["created", "requirements", "design", "tasks", "implementing", "completed"], "templates": {"overview": ".vibecode/templates/spec-templates/overview.md", "requirements": ".vibecode/templates/spec-templates/requirements.md", "design": ".vibecode/templates/spec-templates/design.md", "tasks": ".vibecode/templates/spec-templates/tasks.md"}, "autoGenerate": {"requirements": true, "design": true, "tasks": true}}, "bugs": {"enabled": true, "defaultStatus": "reported", "statusFlow": ["reported", "analyzing", "fixing", "verifying", "resolved"], "severityLevels": ["low", "medium", "high", "critical"], "templates": {"report": ".vibecode/templates/bug-templates/report.md", "analysis": ".vibecode/templates/bug-templates/analysis.md", "fix": ".vibecode/templates/bug-templates/fix.md", "verification": ".vibecode/templates/bug-templates/verification.md"}, "autoGenerate": {"analysis": true, "fix": true, "verification": true}}}, "analysis": {"enabled": true, "modules": ["codebase-analysis", "dependency-mapping", "architecture-assessment", "evolution-tracking"], "outputFormats": ["markdown", "json", "mermaid"]}, "prediction": {"enabled": true, "modules": ["issue-prediction", "performance-assessment", "security-scanning", "debt-measurement"], "timeFrames": ["immediate", "short-term", "medium-term", "long-term"], "riskLevels": ["low", "medium", "high", "critical"]}, "generation": {"enabled": true, "modules": ["code-generation", "refactor-suggestions", "test-generation", "performance-optimization"], "supportedLanguages": ["typescript", "javascript", "python", "java", "go", "rust"], "qualityChecks": ["readability", "maintainability", "performance", "security", "testability"]}, "steering": {"enabled": true, "documents": ["product.md", "tech.md", "structure.md"], "autoUpdate": true, "integrationLevel": "full"}, "notifications": {"enabled": true, "channels": ["console", "file"], "levels": ["info", "warning", "error"]}, "performance": {"maxResponseTime": 2000, "maxMemoryUsage": 536870912, "concurrentTasks": 5, "cacheEnabled": true}, "security": {"sandboxMode": true, "allowedPaths": [".vibecode/", "src/", "package.json", "tsconfig.json"], "blockedOperations": ["exec", "spawn", "network"]}, "subAgents": {"enabled": true, "version": "1.4.0", "qualityGates": {"defaultThreshold": 95, "phaseThresholds": {"requirements": 95, "architecture": 95, "implementation": 85, "quality": 75, "testing": 70, "integration": 75}, "overallThreshold": 95, "strictMode": false}, "agents": {"orchestrator": {"enabled": true, "maxRetries": 3, "maxWorkflowRetries": 2, "timeout": 30000}, "spec": {"enabled": true, "timeout": 15000, "outputFormat": "EARS"}, "architect": {"enabled": true, "timeout": 20000, "preferredStack": "typescript"}, "developer": {"enabled": true, "timeout": 25000, "codeQuality": "A", "improvementBonus": 3}, "quality": {"enabled": true, "timeout": 15000, "securityScan": true, "performanceCheck": true}, "test": {"enabled": true, "timeout": 20000, "coverageTarget": 90, "testTypes": ["unit", "integration"]}}, "workflow": {"phases": ["initialization", "requirements", "architecture", "implementation", "quality", "testing", "integration", "completion"], "autoRestart": true, "continueOnPhaseFailure": true, "progressReporting": true}, "output": {"format": "detailed", "showProgress": true, "showQualityScores": true, "saveResults": true, "resultPath": ".vibecode/workflows/results"}, "communication": {"messageBus": {"timeout": 5000, "retryAttempts": 3, "bufferSize": 1000}, "logging": {"level": "info", "saveToFile": true, "logPath": ".vibecode/workflows/logs"}}}}