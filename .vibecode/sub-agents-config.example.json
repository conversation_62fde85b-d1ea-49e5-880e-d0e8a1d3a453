{"version": "1.4.0", "workflow": {"timeout": 300000, "maxRetries": 2, "qualityThreshold": 95, "phases": ["initialization", "requirements", "architecture", "implementation", "quality", "testing", "integration", "completion"], "phaseThresholds": {"requirements": 90, "architecture": 85, "implementation": 85, "quality": 75, "testing": 70, "integration": 85}, "parallelExecution": false, "saveResults": true, "showProgress": true}, "agents": {"orchestrator": {"enabled": true, "timeout": 60000, "maxRetries": 3, "qualityThreshold": 95, "capabilities": ["workflow-orchestration", "phase-management", "quality-gate-enforcement", "agent-coordination"], "integrations": {}}, "spec": {"enabled": true, "timeout": 45000, "maxRetries": 3, "qualityThreshold": 90, "capabilities": ["requirements-analysis", "user-story-generation", "acceptance-criteria-definition", "requirements-quality-assessment"], "integrations": {}}, "architect": {"enabled": true, "timeout": 60000, "maxRetries": 3, "qualityThreshold": 85, "capabilities": ["system-architecture-design", "technical-stack-selection", "api-design", "data-modeling"], "integrations": {}}, "developer": {"enabled": true, "timeout": 120000, "maxRetries": 3, "qualityThreshold": 85, "capabilities": ["intelligent-code-generation", "code-optimization", "best-practices-implementation", "code-quality-assurance"], "integrations": {}}, "quality": {"enabled": true, "timeout": 60000, "maxRetries": 3, "qualityThreshold": 75, "capabilities": ["code-quality-analysis", "security-vulnerability-scanning", "performance-assessment", "technical-debt-measurement"], "integrations": {}}, "test": {"enabled": true, "timeout": 90000, "maxRetries": 3, "qualityThreshold": 70, "capabilities": ["automated-test-generation", "test-coverage-analysis", "test-quality-assessment", "multiple-frameworks-support"], "integrations": {}}}, "integrations": {"codeAnalysis": {"enabled": true, "tools": ["eslint", "prettier", "typescript"]}, "testing": {"frameworks": ["jest", "vitest", "cypress"], "coverage": {"threshold": 80, "enabled": true}}}}