## Codelf (v2.1.0 - Sub-Agents 完整版)

> 项目使用的技术、工具库及对应依赖版本如下：
> TypeScript（v5.7.3）、Node.js、@modelcontextprotocol/sdk（v1.5.0）、Zod（v3.24.2）
>
> **v2.0.0 重大更新**: 简化配置系统，改为命令行参数方式，符合标准 MCP 调用习惯
> **v2.1.0 革命性更新**: 完整实现 Sub-Agents 系统，四个专业化代理协同工作，95%质量门控

## 项目结构

> 文件级别的分析对于理解项目至关重要。

> 以下是项目的目录结构，并对重要部分进行了注释说明。

root

- .vibecode // 项目文档目录，存放项目相关的说明文档 ✅ (v2.0.0 简化)
  ├── steering/ // Steering 文档目录，持久化项目知识 ✅
  │ ├── product.md // 产品概述文档 ✅
  │ ├── tech.md // 技术栈文档 ✅
  │ └── structure.md // 项目结构文档 ✅
  ├── specs/ // 规范工作流目录 ✅ (v2.0.0 简化结构)
  ├── bugs/ // Bug 修复工作流目录 ✅ (v2.0.0 简化结构)
  ├── workflow-results/ // 工作流结果目录 ✅ (v2.0.0 新增)
  ├── config.json // 简化配置文件 ✅ (v2.0.0 大幅简化)
  └── \*.md // 项目文档文件
- .git // Git 版本控制目录
- .gitignore // Git 忽略文件配置
- src/ // 源代码目录
  ├── analysis/ // 代码分析模块
  ├── prediction/ // 预测分析模块
  ├── generation/ // 智能代码生成模块 🆕
  │ ├── engine.ts // 核心生成引擎
  │ ├── types.ts // 生成相关类型定义 (100+ 接口)
  │ ├── context.ts // 上下文分析器
  │ ├── templates.ts // 代码模板管理系统 (12+ 模板)
  │ ├── quality.ts // 代码质量检查器
  │ ├── nlp.ts // NLP 处理模块 🆕
  │ └── refactor.ts // 重构建议引擎 🆕
  ├── agents/ // Sub-Agents 系统模块 🚀
  │ ├── types.ts // 完整的类型定义系统 (200+ 接口)
  │ ├── base-agent.ts // 基础代理抽象类
  │ ├── communication/ // 通信机制模块
  │ │ └── message-bus.ts // 消息总线实现
  │ ├── orchestrator/ // 编排代理
  │ │ └── orchestrator-agent.ts // 工作流程协调和管理
  │ ├── spec/ // 规格代理
  │ │ └── spec-agent.ts // 需求分析和规格生成
  │ ├── architect/ // 架构代理
  │ │ └── architect-agent.ts // 系统设计和架构规划
  │ ├── developer/ // 开发代理
  │ │ └── developer-agent.ts // 智能代码生成和实现
  │ ├── quality/ // 质量代理
  │ │ └── quality-agent.ts // 代码质量和安全分析
  │ └── test/ // 测试代理
  │ └── test-agent.ts // 自动测试生成和验证
  ├── tools/ // 工具模块 (v2.0.0 简化)
  │ ├── basic.ts // 基础工具 ✅
  │ ├── steering.ts // Steering 工具 ✅
  │ ├── spec-simple.ts // 简化规范工具 🆕 (v2.0.0)
  │ ├── bug-simple.ts // 简化 Bug 工具 🆕 (v2.0.0)
  │ ├── intelligence.ts // 智能分析工具 ✅
  │ └── sub-agents-simple.ts // 简化 Sub-Agents 工具 🆕 (v2.0.0)
  └── server.ts // MCP 服务器配置
- index.ts // 主入口文件，包含 MCP 服务器实现和工具函数定义
- node_modules // Node.js 依赖包目录
- package.json // 项目配置文件，定义依赖和脚本
- pnpm-lock.yaml // pnpm 包管理器锁定文件
- README.md // 英文版项目说明文档
- README_CN.md // 中文版项目说明文档
- tsconfig.json // TypeScript 配置文件
- WORKFLOW_README.md // 工作流程说明文档
- V1_3_0_IMPLEMENTATION_PLAN.md // v1.3.0 实施计划 🆕
- V1_3_0_KICKOFF_REPORT.md // v1.3.0 启动报告 🆕
- V1_3_0_WEEK1_COMPLETION_REPORT.md // Week 1 完成报告 🆕

### 核心功能

该项目是一个 MCP（Model Context Protocol）服务器实现，v2.0.0 版本进行了重大简化，改为命令行参数配置方式，提供以下工具功能：

### v2.0.0 简化成果

- **工具数量**: 26 个 → 17 个 (减少 35%)
- **构建文件大小**: 158KB → 85.68KB (减少 46%)
- **测试成功率**: 75% → 100% (提升 25%)
- **配置复杂度**: 大幅简化，移除复杂配置文件
- **MCP 兼容性**: 完全符合标准 MCP 调用习惯

### v2.1.0 Sub-Agents 革命性升级

- **工具数量**: 17 个 → 20 个 (新增 3 个核心 Sub-Agents 工具)
- **构建文件大小**: 85.68KB → 100.73KB (增加 17.5%)
- **专业化代理**: 4 个专业代理 (规格生成、代码实现、质量验证、测试生成)
- **质量门控**: 95% 自动质量控制系统
- **工作流自动化**: 完整的一键式开发流程

#### 基础工具

1. `get-project-info` - 获取项目详细信息，帮助 AI 更好地理解代码
2. `update-project-info` - 更新项目信息，维护.codelf 目录下的文档
3. `init-codelf` - 初始化.codelf 目录和文件，帮助建立项目文档结构

#### Steering 系统（持久化项目知识）

4. `init-steering` - 初始化 Steering 文档系统，创建产品概述、技术栈和项目结构文档
5. `get-steering` - 获取 Steering 文档内容，用于指导工作流程

#### 规范工作流（Spec Workflow）- v2.0.0 简化

6. `spec-create` - 创建新的规范工作流 (简化参数)
7. `spec-list` - 列出所有规范
8. `spec-status` - 查看规范的当前状态

#### Bug 修复工作流（Bug Fix Workflow）- v2.0.0 简化

9. `bug-create` - 创建新的 Bug 修复工作流 (简化参数)
10. `bug-list` - 列出所有 Bug 报告
11. `bug-status` - 查看 Bug 的当前状态

#### 智能分析系统（Intelligence Analysis）

12. `analyze-codebase` - 综合代码库分析
13. `map-dependencies` - 依赖关系映射和可视化
14. `assess-architecture` - 架构评估和建议
15. `track-evolution` - 项目演进追踪

#### 🚀 Sub-Agents 系统（v2.1.0 完整版）🚀

16. `spec-generation` - 🎯 规格生成专家：创建完整规格文档 (requirements.md + design.md + tasks.md)
17. `spec-executor` - 💻 代码实现专家：基于规格文档实现功能
18. `spec-validation` - 🔍 质量验收专家：95%质量门控 + 多维度评分
19. `spec-testing` - 🧪 测试生成专家：综合测试策略 + 完整测试套件
20. `spec-workflow-status` - 📊 工作流状态管理：进度追踪 + 文件管理

### v2.0.0 移除的工具 (为简化而移除)

- ❌ `spec-requirements`, `spec-design`, `spec-tasks`, `spec-execute` - 合并到 `spec-create`
- ❌ `bug-analyze`, `bug-fix`, `bug-verify` - 合并到 `bug-create`
- ❌ `predict-issues`, `assess-performance`, `scan-security`, `measure-debt` - 预测分析系统
- ❌ `generate-code`, `suggest-refactor`, `generate-tests`, `optimize-performance` - 代码生成系统
- ❌ `vibe-agents` - 代理查询工具

### 工作流程特性

#### 🎯 规范驱动开发流程

- **需求分析** → **设计文档** → **任务分解** → **实施执行**
- 集成 Steering 文档确保项目标准一致性
- 自动生成结构化文档和模板

#### 🐛 Bug 修复工作流程

- **问题报告** → **根因分析** → **修复实施** → **验证测试**
- 支持严重程度分级（low/medium/high/critical）
- 系统化的问题跟踪和状态管理

#### 📋 Steering 文档系统

- **product.md** - 产品愿景、目标用户、核心功能
- **tech.md** - 技术栈、开发工具、技术约束
- **structure.md** - 文件组织、命名约定、代码组织原则

#### 🧠 智能分析系统（v1.1.0）

- **代码语义分析** - TypeScript AST 解析，函数/类结构分析
- **依赖关系映射** - 模块依赖图谱，循环依赖检测
- **架构洞察** - 设计模式识别，SOLID 原则检查
- **质量评估** - 综合质量评分，改进建议生成
- **可视化输出** - Mermaid 图表，质量仪表板

#### 🔮 预测分析系统（v1.2.0）

- **问题预测分析** - 8 种问题类型预测，时间框架预测
- **性能风险评估** - 瓶颈识别，可扩展性问题预测
- **安全漏洞扫描** - 7 类漏洞检测，风险量化评估
- **技术债务量化** - 5 类债务分析，优先级排序
- **预防策略生成** - 具体可执行的行动计划

#### 🚀 智能代码生成系统（v1.3.0）

- **上下文感知生成** - 理解项目风格、架构模式、业务逻辑
- **智能重构建议** - 基于预测分析的重构优化
- **自动测试生成** - 单元测试、集成测试、性能测试
- **性能优化建议** - 算法优化、缓存策略、异步处理
- **质量保证生成** - 生成符合项目标准的高质量代码

#### 🤖 Sub-Agents 系统（v1.4.0 革命性功能）🚀

- **一键式开发流程** - 单命令完成从需求到代码的全流程
- **AI 专家团队协作** - 6 个专业化代理协同工作
- **95% 质量门控** - 企业级质量保证和自动重试
- **完全无人工干预** - 端到端自动化开发流程
- **智能工作流程编排** - 事件驱动的代理通信和协调

**Sub-Agents 团队**：

- 🎯 **Orchestrator Agent** - 工作流程协调和管理
- 📋 **Spec Agent** - 需求分析和规格生成（EARS 格式）
- 🏗️ **Architect Agent** - 系统设计和架构规划
- 💻 **Developer Agent** - 智能代码生成和实现
- 🔍 **Quality Agent** - 代码质量和安全分析
- 🧪 **Test Agent** - 自动测试生成和验证

项目通过 Node.js 实现，使用 TypeScript 进行开发，现已进化为具备完整 AI 编程能力的智能系统：从代码理解、问题预测、主动生成解决方案，到革命性的 Sub-Agents 一键式开发流程，为 AI 代码助手提供全方位的编程支持能力。
