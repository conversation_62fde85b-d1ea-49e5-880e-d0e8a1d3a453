# 规范概述 - {{specName}}

## 📋 基本信息

- **规范名称**: {{specName}}
- **创建时间**: {{createdAt}}
- **当前状态**: {{status}}
- **负责人**: {{assignee}}
- **优先级**: {{priority}}

## 🎯 规范目标

{{description}}

## 📊 成功标准

### 功能性标准

- [ ] 核心功能完整实现
- [ ] 用户界面友好易用
- [ ] 性能指标达到要求
- [ ] 安全性要求满足

### 非功能性标准

- [ ] 代码质量达到 B+ 级别
- [ ] 测试覆盖率 > 80%
- [ ] 文档完整性 > 90%
- [ ] 用户满意度 > 85%

## 🔗 相关资源

### 参考文档

- [项目技术栈](.vibecode/steering/tech.md)
- [项目结构规范](.vibecode/steering/structure.md)
- [产品概述](.vibecode/steering/product.md)

### 相关规范

{{relatedSpecs}}

### 外部依赖

{{externalDependencies}}

## 📈 进度跟踪

### 里程碑

- [ ] **需求分析完成** - 预计时间: {{requirementsDeadline}}
- [ ] **设计文档完成** - 预计时间: {{designDeadline}}
- [ ] **任务分解完成** - 预计时间: {{tasksDeadline}}
- [ ] **开发实施完成** - 预计时间: {{implementationDeadline}}

### 当前进度

- **完成度**: {{completionPercentage}}%
- **剩余工作量**: {{remainingWork}}
- **预计完成时间**: {{estimatedCompletion}}

## 🚨 风险评估

### 技术风险

{{technicalRisks}}

### 资源风险

{{resourceRisks}}

### 时间风险

{{timeRisks}}

## 📝 变更记录

| 日期          | 变更内容 | 变更人      | 版本 |
| ------------- | -------- | ----------- | ---- |
| {{createdAt}} | 初始创建 | {{creator}} | v1.0 |

---

_由 Vibe Coding 工作流程系统自动生成_
_最后更新: {{lastUpdated}}_
