#!/usr/bin/env node

/**
 * 全面测试 vibe-coding MCP 服务器
 * 生成详细的测试报告
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import fs from 'fs';

async function comprehensiveTest() {
  console.log('🚀 开始全面测试 vibe-coding MCP 服务器...\n');
  
  const report = {
    timestamp: new Date().toISOString(),
    serverInfo: null,
    tools: [],
    testResults: [],
    summary: {
      totalTools: 0,
      testedTools: 0,
      passedTests: 0,
      failedTests: 0
    }
  };

  try {
    // 创建客户端传输
    const transport = new StdioClientTransport({
      command: 'node',
      args: ['build/index.js']
    });

    // 创建客户端
    const client = new Client(
      {
        name: "comprehensive-test-client",
        version: "1.0.0"
      },
      {
        capabilities: {}
      }
    );

    // 连接到服务器
    await client.connect(transport);
    console.log('✅ 成功连接到 MCP 服务器');

    // 获取服务器信息
    report.serverInfo = await client.getServerVersion();
    console.log('📋 服务器信息:', report.serverInfo);

    // 列出所有工具
    const toolsResponse = await client.listTools();
    report.tools = toolsResponse.tools;
    report.summary.totalTools = report.tools.length;
    
    console.log(`\n🛠️  发现 ${report.tools.length} 个工具:`);
    
    // 按类别分组工具
    const toolCategories = {
      'Basic': [],
      'Steering': [],
      'Spec': [],
      'Bug': [],
      'Intelligence': [],
      'Prediction': [],
      'Generation': [],
      'Sub-Agents': [],
      'Other': []
    };

    report.tools.forEach(tool => {
      if (tool.name.startsWith('get-project-info') || tool.name.startsWith('update-project-info') || tool.name.startsWith('init-vibe')) {
        toolCategories.Basic.push(tool);
      } else if (tool.name.includes('steering')) {
        toolCategories.Steering.push(tool);
      } else if (tool.name.startsWith('spec-')) {
        toolCategories.Spec.push(tool);
      } else if (tool.name.startsWith('bug-')) {
        toolCategories.Bug.push(tool);
      } else if (tool.name.startsWith('analyze-') || tool.name.startsWith('map-') || tool.name.startsWith('assess-')) {
        toolCategories.Intelligence.push(tool);
      } else if (tool.name.startsWith('predict-')) {
        toolCategories.Prediction.push(tool);
      } else if (tool.name.startsWith('generate-') || tool.name.startsWith('suggest-')) {
        toolCategories.Generation.push(tool);
      } else if (tool.name.includes('vibe-coding')) {
        toolCategories['Sub-Agents'].push(tool);
      } else {
        toolCategories.Other.push(tool);
      }
    });

    // 显示工具分类
    Object.entries(toolCategories).forEach(([category, tools]) => {
      if (tools.length > 0) {
        console.log(`\n📂 ${category} (${tools.length} 个工具):`);
        tools.forEach(tool => {
          console.log(`  • ${tool.name}: ${tool.description.substring(0, 80)}...`);
        });
      }
    });

    // 测试关键工具
    const keyToolsToTest = [
      'get-project-info',
      'init-steering',
      'spec-create',
      'analyze-codebase'
    ];

    console.log('\n🧪 开始测试关键工具...');
    
    for (const toolName of keyToolsToTest) {
      const tool = report.tools.find(t => t.name === toolName);
      if (tool) {
        console.log(`\n🔧 测试工具: ${toolName}`);
        report.summary.testedTools++;
        
        try {
          let result;
          switch (toolName) {
            case 'get-project-info':
              result = await client.callTool({
                name: toolName,
                arguments: { rootPath: process.cwd() }
              });
              break;
            case 'init-steering':
              result = await client.callTool({
                name: toolName,
                arguments: { 
                  rootPath: process.cwd(),
                  projectName: 'vibe-coding',
                  description: 'MCP Server for AI coding assistance'
                }
              });
              break;
            case 'spec-create':
              result = await client.callTool({
                name: toolName,
                arguments: { 
                  rootPath: process.cwd(),
                  title: 'Test Specification',
                  description: 'A test specification for validation'
                }
              });
              break;
            case 'analyze-codebase':
              result = await client.callTool({
                name: toolName,
                arguments: { rootPath: process.cwd() }
              });
              break;
          }
          
          console.log(`  ✅ ${toolName} 测试成功`);
          report.testResults.push({
            tool: toolName,
            status: 'passed',
            result: result.content[0]?.text?.substring(0, 200) + '...'
          });
          report.summary.passedTests++;
          
        } catch (error) {
          console.log(`  ❌ ${toolName} 测试失败: ${error.message}`);
          report.testResults.push({
            tool: toolName,
            status: 'failed',
            error: error.message
          });
          report.summary.failedTests++;
        }
      } else {
        console.log(`  ⚠️  工具 ${toolName} 未找到`);
      }
    }

    // 关闭连接
    await client.close();
    console.log('\n✅ 测试完成，连接已关闭');

    // 生成报告
    const reportContent = JSON.stringify(report, null, 2);
    fs.writeFileSync('test-report.json', reportContent);
    
    // 显示总结
    console.log('\n📊 测试总结:');
    console.log(`  • 总工具数: ${report.summary.totalTools}`);
    console.log(`  • 已测试工具: ${report.summary.testedTools}`);
    console.log(`  • 通过测试: ${report.summary.passedTests}`);
    console.log(`  • 失败测试: ${report.summary.failedTests}`);
    console.log(`  • 成功率: ${((report.summary.passedTests / report.summary.testedTools) * 100).toFixed(1)}%`);
    console.log(`\n📄 详细报告已保存到: test-report.json`);

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
comprehensiveTest().catch(console.error);
