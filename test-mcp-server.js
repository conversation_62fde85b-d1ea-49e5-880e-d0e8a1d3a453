#!/usr/bin/env node

/**
 * 测试 MCP 服务器功能
 * 通过 MCP Inspector 或直接与服务器通信来验证工具
 */

import { spawn } from 'child_process';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function testMCPServer() {
  console.log('🧪 开始测试 MCP 服务器...');
  
  try {
    // 启动 MCP 服务器进程
    const serverProcess = spawn('node', ['build/index.js'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: process.cwd()
    });

    // 创建客户端传输
    const transport = new StdioClientTransport({
      command: 'node',
      args: ['build/index.js']
    });

    // 创建客户端
    const client = new Client(
      {
        name: "test-client",
        version: "1.0.0"
      },
      {
        capabilities: {}
      }
    );

    // 连接到服务器
    await client.connect(transport);
    console.log('✅ 成功连接到 MCP 服务器');

    // 获取服务器信息
    const serverInfo = await client.getServerVersion();
    console.log('📋 服务器信息:', serverInfo);

    // 列出可用工具
    const tools = await client.listTools();
    console.log('🛠️  可用工具数量:', tools.tools.length);
    
    // 显示前几个工具
    console.log('🔧 工具列表:');
    tools.tools.slice(0, 10).forEach((tool, index) => {
      console.log(`  ${index + 1}. ${tool.name}: ${tool.description}`);
    });

    if (tools.tools.length > 10) {
      console.log(`  ... 还有 ${tools.tools.length - 10} 个工具`);
    }

    // 测试一个基础工具
    if (tools.tools.find(t => t.name === 'get-project-info')) {
      console.log('\n🧪 测试 get-project-info 工具...');
      try {
        const result = await client.callTool({
          name: 'get-project-info',
          arguments: {
            rootPath: process.cwd()
          }
        });
        console.log('✅ get-project-info 工具测试成功');
        console.log('📊 结果:', result.content[0]?.text?.substring(0, 200) + '...');
      } catch (error) {
        console.log('❌ get-project-info 工具测试失败:', error.message);
      }
    }

    // 关闭连接
    await client.close();
    console.log('✅ 测试完成，连接已关闭');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
testMCPServer().catch(console.error);
