# Vibe Coding 自动化工作流程

## 🚀 概述

Vibe Coding 现在支持自动化工作流程，包括：

- **Steering 系统** - 持久化项目知识管理
- **规范驱动开发** - 需求 → 设计 → 任务 → 实施
- **Bug 修复工作流** - 报告 → 分析 → 修复 → 验证

## 📁 目录结构

```
.vibecode/
├── steering/                    # Steering 文档目录
│   ├── product.md              # 产品概述
│   ├── tech.md                 # 技术栈
│   └── structure.md            # 项目结构
├── workflows/                   # 工作流程目录
│   ├── specs/                  # 规范工作流
│   │   └── {spec-name}/        # 具体规范目录
│   │       ├── overview.md
│   │       ├── requirements.md
│   │       ├── design.md
│   │       └── tasks.md
│   ├── bugs/                   # Bug 修复工作流
│   │   └── {bug-name}/         # 具体 Bug 目录
│   │       ├── report.md
│   │       ├── analysis.md
│   │       ├── fix.md
│   │       └── verification.md
│   └── config.json             # 工作流程配置
├── templates/                   # 模板目录
└── *.md                        # 项目文档
```

## 🛠️ MCP 工具命令

### Steering 系统

- `init-steering` - 初始化 Steering 文档系统
- `get-steering` - 获取 Steering 文档内容

### 规范工作流 (Spec Workflow)

- `spec-create` - 创建新规范
- `spec-requirements` - 生成需求文档
- `spec-status` - 查看规范状态
- `spec-list` - 列出所有规范

### Bug 修复工作流 (Bug Fix Workflow)

- `bug-create` - 创建 Bug 报告
- `bug-status` - 查看 Bug 状态

## 🎯 使用流程

### 1. 初始化系统

```bash
# 首先初始化 .vibecode 目录（如果还没有）
init-codelf

# 初始化 Steering 文档系统
init-steering
```

### 2. 规范驱动开发流程

```bash
# 1. 创建新规范
spec-create "用户认证系统" "实现安全的用户登录和注册功能"

# 2. 生成需求文档
spec-requirements "user-authentication-system"

# 3. 查看规范状态
spec-status "user-authentication-system"

# 4. 列出所有规范
spec-list
```

### 3. Bug 修复工作流程

```bash
# 1. 创建 Bug 报告
bug-create "登录超时问题" "用户登录后很快被自动登出" --severity high

# 2. 查看 Bug 状态
bug-status "login-timeout-issue"
```

## 📋 Steering 文档说明

Steering 文档提供持久化的项目上下文，确保所有工作流程都遵循项目标准：

### product.md - 产品概述

- 产品愿景和目标
- 目标用户群体
- 核心功能特性
- 成功指标

### tech.md - 技术栈

- 开发语言和框架
- 开发工具和实践
- 技术约束和要求
- 第三方集成

### structure.md - 项目结构

- 文件组织模式
- 命名约定
- 导入模式
- 代码组织原则

## 🔄 工作流程状态

### 规范状态

- `created` 🆕 - 已创建
- `requirements` 📋 - 需求分析中
- `design` 🎨 - 设计阶段
- `tasks` 📝 - 任务分解
- `implementing` ⚙️ - 实施中
- `completed` ✅ - 已完成

### Bug 状态

- `reported` 📋 - 已报告
- `analyzing` 🔍 - 分析中
- `fixing` 🔧 - 修复中
- `verifying` ✅ - 验证中
- `resolved` ✅ - 已解决

## 🎨 特性

### ✅ 持久化项目知识

- Steering 文档确保项目标准一致性
- 自动集成项目上下文到工作流程

### ✅ 结构化工作流程

- 规范驱动的开发流程
- 系统化的 Bug 修复流程

### ✅ 状态管理

- 实时跟踪工作流程进度
- 清晰的状态可视化

### ✅ 模板化文档

- 自动生成标准化文档
- 包含完整的工作流程指导

## 🚀 下一步计划

- [ ] 添加 `spec-design` 工具 - 生成设计文档
- [ ] 添加 `spec-tasks` 工具 - 任务分解
- [ ] 添加 `spec-execute` 工具 - 执行任务
- [ ] 添加 `bug-analyze` 工具 - Bug 分析
- [ ] 添加 `bug-fix` 工具 - Bug 修复
- [ ] 添加 `bug-verify` 工具 - Bug 验证
- [ ] 添加工作流程仪表板
- [ ] 集成 Git 工作流程

## 📚 参考资料

- [Kiro Steering 文档](https://kiro.dev/docs/steering/)
- [Claude Code Spec Workflow](https://github.com/pimzino/claude-code-spec-workflow)

---

_由 Vibe Coding 自动化工作流程系统生成_
