/**
 * Developer Agent - 开发代理 (AI 驱动版本)
 * v1.4.0 - Sub-Agents 革命
 */

import { BaseAgent } from '../base-agent.js';
import {
  AgentMessage,
  AgentContext,
  ImplementationState
} from '../types.js';
import { AIDeveloperAnalysisEngine } from '../engines/ai-analysis-engine.js';

export class DeveloperAgent extends BaseAgent {
  private attemptCount: number = 0;

  constructor(context: AgentContext) {
    super(context);
    this.agentId = 'developer';
    this.agentType = 'developer';
  }

  async processMessage(message: AgentMessage): Promise<AgentMessage> {
    const { action, data } = message.payload;

    switch (action) {
      case 'implement-code':
        return await this.implementCode(data, message);
      default:
        return {
          id: `error-${Date.now()}`,
          type: 'error',
          from: this.agentId,
          to: message.from,
          payload: {
            error: `Unknown action: ${action}`
          },
          timestamp: new Date().toISOString(),
          correlationId: message.correlationId
        };
    }
  }

  private async implementCode(data: any, originalMessage: AgentMessage): Promise<AgentMessage> {
    this.setCurrentTask('AI-driven code implementation planning');

    try {
      this.attemptCount++;
      console.log(`💻 Planning code implementation for: ${data.requirements?.title} (attempt ${this.attemptCount})`);

      // 使用 AI 驱动的代码实现分析引擎
      const analysisEngine = new AIDeveloperAnalysisEngine({
        projectRoot: this.context.projectRoot,
        description: data.requirements?.description || 'Code implementation',
        title: data.requirements?.title || 'Implementation',
        requirements: data.requirements,
        architecture: data.architecture,
        technologyStack: data.architecture?.technicalStack
      });

      const analysisResult = await analysisEngine.analyze();
      const qualityScore = analysisResult.quality;

      const implementationState: ImplementationState = {
        projectStructure: analysisResult.data.projectStructure,
        coreModules: analysisResult.data.coreModules,
        implementationPlan: analysisResult.data.implementationPlan,
        codeQualityStandards: analysisResult.data.codeQualityStandards,
        testingApproach: analysisResult.data.testingApproach,
        qualityScore
      };

      // 更新共享状态
      this.updateSharedState({ implementation: implementationState });

      // 检查质量门控
      const qualityPassed = await this.checkQualityGates('implementation', qualityScore);

      console.log(`💻 Code implementation planned with quality score: ${qualityScore}% (confidence: ${analysisResult.confidence}%)`);

      return {
        id: `dev-response-${Date.now()}`,
        type: 'response',
        from: this.agentId,
        to: originalMessage.from,
        payload: {
          phase: 'implementation',
          success: qualityPassed,
          result: implementationState,
          qualityScore,
          confidence: analysisResult.confidence,
          metadata: analysisResult.metadata,
          message: qualityPassed ? 'Code implementation planned successfully' : 'Implementation quality below threshold'
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };

    } catch (error) {
      console.error('❌ Error planning code implementation:', error);

      return {
        id: `dev-error-${Date.now()}`,
        type: 'response',
        from: this.agentId,
        to: originalMessage.from,
        payload: {
          phase: 'implementation',
          success: false,
          error: error instanceof Error ? error.message : String(error),
          qualityScore: 0
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };
    }
  }

  async executeTask(taskData: any): Promise<any> {
    return await this.implementCode(taskData, {
      id: 'direct-task',
      type: 'request',
      from: 'user',
      to: this.agentId,
      payload: { action: 'implement-code', data: taskData },
      timestamp: new Date().toISOString()
    });
  }

  getCapabilities(): string[] {
    return [
      'AI-driven code implementation planning',
      'Intelligent project structure design',
      'Code quality standards definition',
      'Testing approach recommendation',
      'Performance optimization strategies',
      'Best practices enforcement',
      'Technology stack optimization',
      'Modular architecture design'
    ];
  }
}
