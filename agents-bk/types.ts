/**
 * Sub-Agents 系统类型定义
 * v1.4.0 - Sub-Agents 革命
 */

export interface AgentMessage {
  id: string;
  type: 'request' | 'response' | 'notification' | 'error';
  from: string;
  to: string;
  payload: any;
  timestamp: string;
  correlationId?: string;
}

export interface AgentContext {
  projectRoot: string;
  workflowId: string;
  currentPhase: WorkflowPhase;
  sharedState: SharedState;
  qualityGates: QualityGate[];
  config?: any; // Sub-Agents 配置
}

export interface SharedState {
  requirements?: RequirementsState;
  architecture?: ArchitectureState;
  implementation?: ImplementationState;
  quality?: QualityState;
  testing?: TestingState;
}

export interface RequirementsState {
  specName: string;
  title: string;
  description: string;
  userStories: UserStory[];
  acceptanceCriteria: AcceptanceCriteria[];
  qualityScore: number;
}

export interface ArchitectureState {
  designDocument: string;
  technicalStack: TechnicalStack;
  apiDesign: ApiDesign;
  dataModel: DataModel;
  qualityScore: number;
}

export interface ImplementationState {
  projectStructure?: any;
  coreModules?: any[];
  implementationPlan?: any;
  codeQualityStandards?: any;
  testingApproach?: any;
  generatedFiles?: GeneratedFile[];
  codeQuality?: CodeQuality;
  refactoringSuggestions?: RefactoringSuggestion[];
  qualityScore: number;
}

export interface QualityState {
  qualityMetrics?: any;
  securityIssues?: any[];
  performanceIssues?: any[];
  maintainabilityIssues?: any[];
  testCoverage?: any;
  recommendations?: string[];
  codeAnalysis?: CodeAnalysisResult;
  securityScan?: SecurityScanResult;
  performanceAssessment?: PerformanceAssessment;
  overallScore: number;
}

export interface TestingState {
  testStrategy?: any;
  unitTests?: any[];
  integrationTests?: any[];
  e2eTests?: any[];
  performanceTests?: any[];
  securityTests?: any[];
  automationFramework?: any;
  cicdIntegration?: any;
  coverageTargets?: any;
  testFiles?: TestFile[];
  coverageReport?: CoverageReport;
  testResults?: TestResult[];
  qualityScore: number;
}

export interface UserStory {
  id: string;
  title: string;
  description: string;
  acceptanceCriteria: string[];
  priority: 'high' | 'medium' | 'low';
  estimatedHours: number;
}

export interface AcceptanceCriteria {
  id: string;
  description: string;
  testable: boolean;
  priority: 'must' | 'should' | 'could';
}

export interface TechnicalStack {
  frontend: string[];
  backend: string[];
  database: string[];
  testing: string[];
  deployment: string[];
}

export interface ApiDesign {
  endpoints: ApiEndpoint[];
  authentication: AuthenticationMethod;
  dataFormats: string[];
}

export interface ApiEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  description: string;
  parameters: Parameter[];
  responses: Response[];
}

export interface Parameter {
  name: string;
  type: string;
  required: boolean;
  description: string;
}

export interface Response {
  statusCode: number;
  description: string;
  schema?: any;
}

export interface DataModel {
  entities: Entity[];
  relationships: Relationship[];
}

export interface Entity {
  name: string;
  fields: Field[];
  constraints: Constraint[];
}

export interface Field {
  name: string;
  type: string;
  required: boolean;
  unique: boolean;
  description: string;
}

export interface Relationship {
  from: string;
  to: string;
  type: 'one-to-one' | 'one-to-many' | 'many-to-many';
  description: string;
}

export interface Constraint {
  type: 'primary-key' | 'foreign-key' | 'unique' | 'check';
  fields: string[];
  description: string;
}

export interface GeneratedFile {
  path: string;
  content: string;
  type: 'source' | 'test' | 'config' | 'documentation';
  language: string;
  qualityScore: number;
}

export interface CodeQuality {
  readability: number;
  maintainability: number;
  performance: number;
  security: number;
  testability: number;
  overallGrade: string;
}

export interface RefactoringSuggestion {
  file: string;
  line: number;
  type: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: number;
}

export interface TestFile {
  path: string;
  content: string;
  type: 'unit' | 'integration' | 'e2e' | 'performance';
  framework: string;
  coverageTarget: number;
}

export interface CoverageReport {
  overall: number;
  byFile: { [file: string]: number };
  byType: { [type: string]: number };
  uncoveredLines: UncoveredLine[];
}

export interface UncoveredLine {
  file: string;
  line: number;
  reason: string;
}

export interface TestResult {
  file: string;
  passed: number;
  failed: number;
  skipped: number;
  duration: number;
  failures: TestFailure[];
}

export interface TestFailure {
  test: string;
  error: string;
  stack: string;
}

export interface QualityGate {
  name: string;
  phase: WorkflowPhase;
  threshold: number;
  metric: string;
  required: boolean;
}

export interface WorkflowResult {
  success: boolean;
  workflowId: string;
  duration: number;
  phases: PhaseResult[];
  finalState: SharedState;
  qualityReport: QualityReport;
}

export interface PhaseResult {
  phase: WorkflowPhase;
  agent: string;
  success: boolean;
  duration: number;
  qualityScore: number;
  outputs: any[];
  errors: string[];
}

export interface QualityReport {
  overallScore: number;
  phaseScores: { [phase: string]: number };
  gatesPassed: number;
  gatesTotal: number;
  recommendations: string[];
}

export type WorkflowPhase =
  | 'initialization'
  | 'requirements'
  | 'architecture'
  | 'implementation'
  | 'quality'
  | 'testing'
  | 'integration'
  | 'completion';

export type AgentType =
  | 'orchestrator'
  | 'spec'
  | 'architect'
  | 'developer'
  | 'quality'
  | 'test';

export type AuthenticationMethod =
  | 'jwt'
  | 'oauth2'
  | 'api-key'
  | 'session'
  | 'basic';

export interface CodeAnalysisResult {
  complexity: number;
  maintainability: number;
  duplications: number;
  issues: Issue[];
  metrics: { [key: string]: number };
}

export interface Issue {
  file: string;
  line: number;
  severity: 'info' | 'minor' | 'major' | 'critical';
  type: string;
  message: string;
}

export interface SecurityScanResult {
  vulnerabilities: Vulnerability[];
  riskScore: number;
  recommendations: string[];
}

export interface Vulnerability {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  file: string;
  line: number;
  description: string;
  solution: string;
}

export interface PerformanceAssessment {
  bottlenecks: Bottleneck[];
  optimizations: Optimization[];
  score: number;
}

export interface Bottleneck {
  type: string;
  location: string;
  impact: 'low' | 'medium' | 'high';
  description: string;
}

export interface Optimization {
  type: string;
  description: string;
  effort: number;
  impact: number;
  priority: 'low' | 'medium' | 'high';
}
