/**
 * Quality Agent - 质量代理
 * v1.4.0 - Sub-Agents 革命
 */

import { BaseAgent } from '../base-agent.js';
import {
  AgentMessage,
  AgentContext,
  QualityState
} from '../types.js';
import { AIQualityAnalysisEngine } from '../engines/ai-analysis-engine.js';

export class QualityAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super('quality', context);
  }

  protected async onStart(): Promise<void> {
    console.log('🔍 Quality Agent ready to analyze quality');
  }

  protected async onStop(): Promise<void> {
    console.log('🔍 Quality Agent stopped');
  }

  protected async processMessage(message: AgentMessage): Promise<AgentMessage | null> {
    switch (message.type) {
      case 'request':
        return await this.handleRequest(message);
      default:
        return null;
    }
  }

  private async handleRequest(message: AgentMessage): Promise<AgentMessage | null> {
    const { action, data } = message.payload;

    switch (action) {
      case 'analyze-quality':
        return await this.analyzeQuality(data, message);
      default:
        return {
          id: `error-${Date.now()}`,
          type: 'error',
          to: message.from,
          payload: { error: `Unknown action: ${action}` },
          timestamp: new Date().toISOString(),
          correlationId: message.correlationId
        };
    }
  }

  private async analyzeQuality(data: any, originalMessage: AgentMessage): Promise<AgentMessage> {
    this.setCurrentTask('AI-driven quality analysis');

    try {
      console.log(`🔍 Analyzing quality for implementation`);

      // 使用 AI 驱动的质量分析引擎
      const analysisEngine = new AIQualityAnalysisEngine({
        projectRoot: this.context.projectRoot,
        description: data.requirements?.description || 'Quality analysis',
        title: data.requirements?.title || 'Quality Assessment',
        requirements: data.requirements,
        architecture: data.architecture,
        existingCode: JSON.stringify(data.implementation || {})
      });

      const analysisResult = await analysisEngine.analyze();
      const overallScore = analysisResult.quality;

      const qualityState: QualityState = {
        qualityMetrics: analysisResult.data.qualityMetrics,
        securityIssues: analysisResult.data.securityIssues,
        performanceIssues: analysisResult.data.performanceIssues,
        maintainabilityIssues: analysisResult.data.maintainabilityIssues,
        testCoverage: analysisResult.data.testCoverage,
        recommendations: analysisResult.data.recommendations,
        overallScore
      };

      // 更新共享状态
      this.updateSharedState({ quality: qualityState });

      // 检查质量门控
      const qualityPassed = await this.checkQualityGates('quality', overallScore);

      console.log(`🔍 Quality analysis completed with score: ${overallScore}% (confidence: ${analysisResult.confidence}%)`);

      return {
        id: `quality-response-${Date.now()}`,
        type: 'response',
        from: this.agentId,
        to: originalMessage.from,
        payload: {
          phase: 'quality',
          success: qualityPassed,
          result: qualityState,
          qualityScore: overallScore,
          confidence: analysisResult.confidence,
          metadata: analysisResult.metadata,
          message: qualityPassed ? 'Quality analysis completed successfully' : 'Quality below threshold'
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };

    } catch (error) {
      console.error('❌ Error analyzing quality:', error);

      return {
        id: `quality-error-${Date.now()}`,
        type: 'response',
        from: this.agentId,
        to: originalMessage.from,
        payload: {
          phase: 'quality',
          success: false,
          error: error instanceof Error ? error.message : String(error),
          qualityScore: 0
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };
    }
  }

  // 所有硬编码的质量分析方法已被 AIQualityAnalysisEngine 替代
  // 现在使用 AI 驱动的智能质量分析

  // 所有硬编码的质量分析方法已被 AIQualityAnalysisEngine 替代

  async executeTask(taskData: any): Promise<any> {
    return await this.analyzeQuality(taskData, {
      id: 'direct-task',
      type: 'request',
      from: 'user',
      to: this.agentId,
      payload: { action: 'analyze-quality', data: taskData },
      timestamp: new Date().toISOString()
    });
  }

  getCapabilities(): string[] {
    return [
      'AI-driven code quality analysis',
      'Intelligent security vulnerability scanning',
      'Performance bottleneck detection',
      'Technical debt measurement',
      'Best practices validation',
      'Quality metrics calculation',
      'Maintainability assessment',
      'Test coverage analysis'
    ];
  }
}
