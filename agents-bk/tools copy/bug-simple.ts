/**
 * Simplified Bug workflow MCP tools
 * No configuration files, all parameters via tool calls
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { z } from "zod";
import { ROOT_PATH_DESC } from "../utils/const.js";

/**
 * Register simplified bug workflow tools
 */
export function registerBugTools(server: McpServer): void {
  // Create bug report
  server.tool(
    "bug-create",
    "Create new bug fix workflow",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
      title: z.string().describe("Bug title"),
      description: z.string().describe("Bug description"),
      severity: z.enum(["low", "medium", "high", "critical"]).optional().default("medium").describe("Bug severity level"),
    },
    async ({ rootPath, title, description, severity = "medium" }) => {
      try {
        // Ensure .vibecode directory exists
        const vibecodePath = path.join(rootPath, ".vibecode");
        if (!existsSync(vibecodePath)) {
          await fs.mkdir(vibecodePath, { recursive: true });
        }

        // Create bugs directory
        const bugsPath = path.join(vibecodePath, "bugs");
        if (!existsSync(bugsPath)) {
          await fs.mkdir(bugsPath, { recursive: true });
        }

        // Generate bug name from title
        const bugName = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
        const bugPath = path.join(bugsPath, `${bugName}.md`);

        // Check if bug already exists
        if (existsSync(bugPath)) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Bug report "${title}" already exists at ${bugPath}`,
              },
            ],
          };
        }

        // Create bug report document
        const timestamp = new Date().toISOString();
        const severityEmoji = {
          low: "🟢",
          medium: "🟡",
          high: "🟠",
          critical: "🔴"
        };

        const bugContent = `# ${title}

**Created:** ${timestamp}
**Status:** Reported
**Severity:** ${severityEmoji[severity]} ${severity.toUpperCase()}

## Description
${description}

## Steps to Reproduce
_To be defined_

## Expected Behavior
_To be defined_

## Actual Behavior
_To be defined_

## Analysis
_To be added during analysis_

## Fix Implementation
_To be added during fix_

## Verification
_To be added during verification_
`;

        await fs.writeFile(bugPath, bugContent, "utf-8");

        return {
          content: [
            {
              type: "text",
              text: `✅ Bug report "${title}" created successfully at ${bugPath}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Error creating bug report: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // List bugs
  server.tool(
    "bug-list",
    "List all bug reports",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
    },
    async ({ rootPath }) => {
      try {
        const bugsPath = path.join(rootPath, ".vibecode", "bugs");

        if (!existsSync(bugsPath)) {
          return {
            content: [
              {
                type: "text",
                text: "🐛 No bug reports found. Use bug-create to create your first bug report.",
              },
            ],
          };
        }

        const files = await fs.readdir(bugsPath);
        const bugFiles = files.filter(file => file.endsWith('.md'));

        if (bugFiles.length === 0) {
          return {
            content: [
              {
                type: "text",
                text: "🐛 No bug reports found. Use bug-create to create your first bug report.",
              },
            ],
          };
        }

        const bugs = [];
        for (const file of bugFiles) {
          const filePath = path.join(bugsPath, file);
          const content = await fs.readFile(filePath, "utf-8");
          const lines = content.split('\n');
          const title = lines[0]?.replace(/^#\s*/, '') || file.replace('.md', '');
          const statusLine = lines.find(line => line.startsWith('**Status:**'));
          const status = statusLine?.replace('**Status:**', '').trim() || 'Unknown';
          const severityLine = lines.find(line => line.startsWith('**Severity:**'));
          const severity = severityLine?.replace('**Severity:**', '').trim() || 'Unknown';

          bugs.push(`• ${title} (${file}) - Status: ${status} - Severity: ${severity}`);
        }

        return {
          content: [
            {
              type: "text",
              text: `🐛 Found ${bugs.length} bug report(s):\n\n${bugs.join('\n')}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Error listing bug reports: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // View bug status
  server.tool(
    "bug-status",
    "View current status of bugs",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
      bugName: z.string().optional().describe("Optional: specific bug name to check"),
    },
    async ({ rootPath, bugName }) => {
      try {
        const bugsPath = path.join(rootPath, ".vibecode", "bugs");

        if (!existsSync(bugsPath)) {
          return {
            content: [
              {
                type: "text",
                text: "🐛 No bug reports directory found.",
              },
            ],
          };
        }

        if (bugName) {
          // Show specific bug
          const bugPath = path.join(bugsPath, `${bugName}.md`);
          if (!existsSync(bugPath)) {
            return {
              content: [
                {
                  type: "text",
                  text: `❌ Bug report "${bugName}" not found.`,
                },
              ],
            };
          }

          const content = await fs.readFile(bugPath, "utf-8");
          return {
            content: [
              {
                type: "text",
                text: `🐛 Bug Report: ${bugName}\n\n${content}`,
              },
            ],
          };
        } else {
          // Show all bugs status
          const files = await fs.readdir(bugsPath);
          const bugFiles = files.filter(file => file.endsWith('.md'));

          if (bugFiles.length === 0) {
            return {
              content: [
                {
                  type: "text",
                  text: "🐛 No bug reports found.",
                },
              ],
            };
          }

          const statusList = [];
          for (const file of bugFiles) {
            const filePath = path.join(bugsPath, file);
            const content = await fs.readFile(filePath, "utf-8");
            const lines = content.split('\n');
            const title = lines[0]?.replace(/^#\s*/, '') || file.replace('.md', '');
            const statusLine = lines.find(line => line.startsWith('**Status:**'));
            const status = statusLine?.replace('**Status:**', '').trim() || 'Unknown';
            const severityLine = lines.find(line => line.startsWith('**Severity:**'));
            const severity = severityLine?.replace('**Severity:**', '').trim() || 'Unknown';

            statusList.push(`🐛 ${title}: ${status} (${severity})`);
          }

          return {
            content: [
              {
                type: "text",
                text: `📊 Bug Reports Status:\n\n${statusList.join('\n')}`,
              },
            ],
          };
        }
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Error checking bug status: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );
}
