/**
 * Test Agent - 测试代理 (AI 驱动版本)
 * v1.4.0 - Sub-Agents 革命
 */

import { BaseAgent } from '../base-agent.js';
import {
  AgentMessage,
  AgentContext,
  TestingState
} from '../types.js';
import { AITestAnalysisEngine } from '../engines/ai-analysis-engine.js';

export class TestAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super(context);
    this.agentId = 'test';
    this.agentType = 'test';
  }

  async processMessage(message: AgentMessage): Promise<AgentMessage> {
    const { action, data } = message.payload;

    switch (action) {
      case 'generate-tests':
        return await this.generateTests(data, message);
      default:
        return {
          id: `error-${Date.now()}`,
          type: 'error',
          from: this.agentId,
          to: message.from,
          payload: {
            error: `Unknown action: ${action}`
          },
          timestamp: new Date().toISOString(),
          correlationId: message.correlationId
        };
    }
  }

  private async generateTests(data: any, originalMessage: AgentMessage): Promise<AgentMessage> {
    this.setCurrentTask('AI-driven test generation');

    try {
      console.log(`🧪 Generating tests for implementation`);

      // 使用 AI 驱动的测试分析引擎
      const analysisEngine = new AITestAnalysisEngine({
        projectRoot: this.context.projectRoot,
        description: data.requirements?.description || 'Test generation',
        title: data.requirements?.title || 'Test Suite',
        requirements: data.requirements,
        architecture: data.architecture,
        implementation: data.implementation
      });

      const analysisResult = await analysisEngine.analyze();
      const qualityScore = analysisResult.quality;

      const testingState: TestingState = {
        testStrategy: analysisResult.data.testStrategy,
        unitTests: analysisResult.data.unitTests,
        integrationTests: analysisResult.data.integrationTests,
        e2eTests: analysisResult.data.e2eTests,
        performanceTests: analysisResult.data.performanceTests,
        securityTests: analysisResult.data.securityTests,
        automationFramework: analysisResult.data.automationFramework,
        cicdIntegration: analysisResult.data.cicdIntegration,
        coverageTargets: analysisResult.data.coverageTargets,
        qualityScore
      };

      // 更新共享状态
      this.updateSharedState({ testing: testingState });

      // 检查质量门控
      const qualityPassed = await this.checkQualityGates('testing', qualityScore);

      console.log(`🧪 Tests generated with quality score: ${qualityScore}% (confidence: ${analysisResult.confidence}%)`);

      return {
        id: `test-response-${Date.now()}`,
        type: 'response',
        from: this.agentId,
        to: originalMessage.from,
        payload: {
          phase: 'testing',
          success: qualityPassed,
          result: testingState,
          qualityScore,
          confidence: analysisResult.confidence,
          metadata: analysisResult.metadata,
          message: qualityPassed ? 'Tests generated successfully' : 'Test coverage below threshold'
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };

    } catch (error) {
      console.error('❌ Error generating tests:', error);

      return {
        id: `test-error-${Date.now()}`,
        type: 'response',
        from: this.agentId,
        to: originalMessage.from,
        payload: {
          phase: 'testing',
          success: false,
          error: error instanceof Error ? error.message : String(error),
          qualityScore: 0
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };
    }
  }

  async executeTask(taskData: any): Promise<any> {
    return await this.generateTests(taskData, {
      id: 'direct-task',
      type: 'request',
      from: 'user',
      to: this.agentId,
      payload: { action: 'generate-tests', data: taskData },
      timestamp: new Date().toISOString()
    });
  }

  getCapabilities(): string[] {
    return [
      'AI-driven test strategy design',
      'Intelligent unit test generation',
      'Integration test planning',
      'End-to-end test scenarios',
      'Performance test design',
      'Security test planning',
      'Test automation framework setup',
      'CI/CD integration planning',
      'Coverage analysis and optimization'
    ];
  }
}
