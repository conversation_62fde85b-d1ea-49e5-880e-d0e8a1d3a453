<template>
  <div class="{{kebabCase name}}">
    <!-- {{description}} -->
    {{#each elements}}
    <{{tag}}{{#if attributes}} {{#each attributes}}{{name}}="{{value}}"{{#unless @last}} {{/unless}}{{/each}}{{/if}}>
      {{#if content}}{{content}}{{/if}}
    </{{tag}}>
    {{/each}}
  </div>
</template>

<script lang="ts" setup>
{{#each imports}}
import {{#if isDefault}}{{name}}{{else}}{ {{name}} }{{/if}} from '{{path}}';
{{/each}}

{{#if hasProps}}
interface Props {
  {{#each props}}
  {{name}}{{#if isOptional}}?{{/if}}: {{type}};
  {{/each}}
}

const props = defineProps<Props>();
{{/if}}

{{#each composables}}
const {{name}} = {{functionCall}};
{{/each}}

{{#each refs}}
const {{name}} = ref<{{type}}>({{defaultValue}});
{{/each}}

{{#each computed}}
const {{name}} = computed(() => {
  // Implement {{name}} computation
  return {{defaultValue}};
});
{{/each}}

{{#each methods}}
const {{name}} = {{#if isAsync}}async {{/if}}({{#each parameters}}{{name}}: {{type}}{{#unless @last}}, {{/unless}}{{/each}}){{#if returnType}}: {{returnType}}{{/if}} => {
  // Implement {{name}} method
};
{{/each}}

{{#if hasWatchers}}
{{#each watchers}}
watch({{source}}, ({{newValue}}{{#if hasOldValue}}, {{oldValue}}{{/if}}) => {
  // Handle {{source}} change
});
{{/each}}
{{/if}}

{{#if hasLifecycle}}
{{#each lifecycle}}
{{name}}(() => {
  // {{description}}
});
{{/each}}
{{/if}}
</script>

{{#if hasStyles}}
<style{{#if scoped}} scoped{{/if}}{{#if lang}} lang="{{lang}}"{{/if}}>
.{{kebabCase name}} {
  /* Component styles */
}

{{#each styles}}
{{selector}} {
  {{#each properties}}
  {{property}}: {{value}};
  {{/each}}
}
{{/each}}
</style>
{{/if}}
