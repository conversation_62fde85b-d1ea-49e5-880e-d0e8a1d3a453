/**
 * {{description}}
 */
export class {{className}} {
  {{#each properties}}
  {{#if isPrivate}}private {{/if}}{{#if isProtected}}protected {{/if}}{{#if isStatic}}static {{/if}}{{name}}: {{type}};
  {{/each}}

  constructor({{#each constructorParams}}{{name}}: {{type}}{{#unless @last}}, {{/unless}}{{/each}}) {
    {{#each constructorParams}}
    this.{{name}} = {{name}};
    {{/each}}
  }

  {{#each methods}}
  {{#if isPrivate}}private {{/if}}{{#if isProtected}}protected {{/if}}{{#if isStatic}}static {{/if}}{{#if isAsync}}async {{/if}}{{name}}({{#each parameters}}{{name}}: {{type}}{{#unless @last}}, {{/unless}}{{/each}}){{#if returnType}}: {{returnType}}{{/if}} {
    // Implementation for {{name}} method
    throw new Error('Method {{name}} not yet implemented');
  }
  {{/each}}
}
