{{#if framework}}
{{#if (eq framework "jest")}}
import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
{{else if (eq framework "vitest")}}
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
{{else}}
import { describe, it, expect, beforeEach, afterEach } from '{{framework}}';
{{/if}}
{{/if}}

{{#each imports}}
import {{#if isDefault}}{{name}}{{else}}{ {{name}} }{{/if}} from '{{path}}';
{{/each}}

describe('{{testSuiteName}}', () => {
  {{#if hasSetup}}
  {{#each setupVariables}}
  let {{name}}: {{type}};
  {{/each}}

  beforeEach(() => {
    {{#each setupVariables}}
    {{name}} = {{defaultValue}};
    {{/each}}
    {{#each setupActions}}
    {{action}};
    {{/each}}
  });
  {{/if}}

  {{#if hasTeardown}}
  afterEach(() => {
    {{#each teardownActions}}
    {{action}};
    {{/each}}
  });
  {{/if}}

  {{#each testCases}}
  {{#if isSkipped}}it.skip{{else if isOnly}}it.only{{else}}it{{/if}}('{{description}}', async () => {
    {{#if hasArrange}}
    // Arrange
    {{#each arrangeSteps}}
    {{step}};
    {{/each}}
    {{/if}}

    {{#if hasAct}}
    // Act
    {{#if isAsync}}
    const result = await {{actionCall}};
    {{else}}
    const result = {{actionCall}};
    {{/if}}
    {{/if}}

    {{#if hasAssert}}
    // Assert
    {{#each assertions}}
    {{#if isNegative}}
    expect({{actual}}).not.{{matcher}}({{expected}});
    {{else}}
    expect({{actual}}).{{matcher}}({{expected}});
    {{/if}}
    {{/each}}
    {{/if}}
  });

  {{/each}}

  {{#if hasNestedSuites}}
  {{#each nestedSuites}}
  describe('{{name}}', () => {
    {{#each testCases}}
    it('{{description}}', async () => {
      // Implement test for {{description}}
    });
    {{/each}}
  });
  {{/each}}
  {{/if}}

  {{#if hasMocks}}
  describe('Mocked scenarios', () => {
    {{#each mocks}}
    it('should handle {{scenario}}', async () => {
      // Mock {{target}}
      {{#if framework}}
      {{#if (eq framework "jest")}}
      const mock{{capitalize target}} = jest.fn().mockResolvedValue({{mockValue}});
      {{else if (eq framework "vitest")}}
      const mock{{capitalize target}} = vi.fn().mockResolvedValue({{mockValue}});
      {{/if}}
      {{/if}}

      // Test with mock
      const result = await {{testCall}};
      
      expect(mock{{capitalize target}}).toHaveBeenCalledWith({{expectedArgs}});
      expect(result).{{assertion}};
    });
    {{/each}}
  });
  {{/if}}

  {{#if hasErrorCases}}
  describe('Error handling', () => {
    {{#each errorCases}}
    it('should handle {{errorType}}', async () => {
      {{#if hasSetup}}
      // Setup error condition
      {{setupError}};
      {{/if}}

      {{#if shouldThrow}}
      await expect({{actionCall}}).rejects.toThrow('{{expectedError}}');
      {{else}}
      const result = await {{actionCall}};
      expect(result.error).toBe('{{expectedError}}');
      {{/if}}
    });
    {{/each}}
  });
  {{/if}}
});
