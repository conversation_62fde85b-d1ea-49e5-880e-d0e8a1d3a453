import React{{#if hasState}}, { useState }{{/if}}{{#if hasEffects}}, { useEffect }{{/if}} from 'react';

interface {{name}}Props {
  {{#each props}}
  {{name}}{{#if isOptional}}?{{/if}}: {{type}};
  {{/each}}
}

/**
 * {{description}}
 */
export const {{name}}: React.FC<{{name}}Props> = ({{#if props.length > 0}}{ {{#each props}}{{name}}{{#unless @last}}, {{/unless}}{{/each}} }{{/if}}) => {
  {{#each state}}
  const [{{name}}, set{{capitalize name}}] = useState<{{type}}>({{defaultValue}});
  {{/each}}

  {{#if hasEffects}}
  useEffect(() => {
    // Implement component effects
  }, []);
  {{/if}}

  {{#each handlers}}
  const {{name}} = {{#if isAsync}}async {{/if}}({{#each parameters}}{{name}}: {{type}}{{#unless @last}}, {{/unless}}{{/each}}) => {
    // Implement {{name}} handler
  };
  {{/each}}

  return (
    <div className="{{kebabCase name}}">
      {/* Implement component JSX */}
      <h1>{{name}} Component</h1>
    </div>
  );
};
