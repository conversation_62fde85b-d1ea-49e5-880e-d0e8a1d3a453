import { Router, Request, Response, NextFunction } from 'express';
{{#each imports}}
import {{#if isDefault}}{{name}}{{else}}{ {{name}} }{{/if}} from '{{path}}';
{{/each}}

const router = Router();

{{#each middlewares}}
/**
 * {{description}}
 */
const {{name}} = {{#if isAsync}}async {{/if}}(req: Request, res: Response, next: NextFunction) => {
  try {
    // Implement {{name}} middleware
    next();
  } catch (error) {
    next(error);
  }
};
{{/each}}

{{#each routes}}
/**
 * {{description}}
 * {{#each parameters}}
 * @param {{name}} - {{description}}
 * {{/each}}
 */
router.{{method}}('{{path}}'{{#if middlewares}}, {{#each middlewares}}{{name}}{{#unless @last}}, {{/unless}}{{/each}}{{/if}}, async (req: Request, res: Response, next: NextFunction) => {
  try {
    {{#if hasValidation}}
    // Validate request
    const validationResult = validate{{capitalize name}}Request(req);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: validationResult.errors
      });
    }
    {{/if}}

    {{#if hasAuth}}
    // Check authentication
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }
    {{/if}}

    // Process {{name}} logic
    const result = await process{{capitalize name}}({{#each parameters}}req.{{source}}.{{name}}{{#unless @last}}, {{/unless}}{{/each}});

    res.status({{successStatus}}).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error in {{name}}:', error);
    next(error);
  }
});

{{#if hasValidation}}
/**
 * Validate {{name}} request
 */
function validate{{capitalize name}}Request(req: Request) {
  // Implement validation logic for {{name}}
  return { success: true, errors: [] };
}
{{/if}}

/**
 * Process {{name}} business logic
 */
async function process{{capitalize name}}({{#each parameters}}{{name}}: {{type}}{{#unless @last}}, {{/unless}}{{/each}}) {
  // Implement business logic for {{name}}
  return { message: '{{name}} completed successfully' };
}
{{/each}}

export default router;
